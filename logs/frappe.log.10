2025-05-12 14:26:22,122 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'user1', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:26:26,515 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': '<EMAIL>', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:26:42,014 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'cmd': 'login', 'usr': 'user1', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-12 14:26:51,325 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': '<EMAIL>', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-13 19:05:23,912 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': '<EMAIL>', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-13 19:07:07,497 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:08:11,082 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'test1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:08:25,424 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'test1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:09:10,562 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'test1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:28:29,933 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:29:54,284 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 17:33:37,156 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'fff'}
2025-05-14 17:34:39,256 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'fff'}
2025-05-14 17:43:27,947 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'uuu'}
2025-05-14 18:03:12,442 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'id': 'ddd', 'parent_category': 'All Item Groups', 'is_group': 0}
2025-05-14 18:03:34,784 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'id': 'ddd', 'parent_category': 'All Item Groups', 'is_group': 0}
2025-05-14 18:03:53,524 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-14 18:03:57,825 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-14 18:04:42,761 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'id': 'ee', 'parent_category': 'All Item Groups', 'is_group': 0}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 22:17:38,622 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 22:18:00,538 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'xx'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-14 22:41:00,933 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-14 22:44:16,789 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'id': 'jjjj', 'parent_category': 'All Item Groups', 'is_group': 0}
2025-05-14 22:45:47,506 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-14 22:45:51,777 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-14 22:49:04,362 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test'}
2025-05-14 22:49:12,557 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2'}
2025-05-14 22:55:13,580 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'uuu'}
2025-05-16 22:43:12,823 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'doc': '{"name":"LR-40","owner":"Administrator","creation":"2025-05-16 20:24:53.946644","modified":"2025-05-16 20:25:35.714233","modified_by":"Administrator","docstatus":0,"idx":0,"status":"Pending i-Score","creation_date":"2024-12-11","naming_series":"LR-.#.","ngo":"test 1","ngo_name":"test 1","publication_number":"9090","license_number":"090","address":"iis","email":"<EMAIL>","publication_date":"2020-01-01","license_date":"2019-01-01","contact_full_name":"irish","contact_email":"<EMAIL>","contact_nid":"29001019990999","contact_mobile_no":"01000898989","contact_nid_front":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-fadca.png","contact_nid_back":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-f0589.png","treasurer_full_name":"tersely","treasurer_nid":"29001018889888","treasurer_nid_front":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-fadca.png","treasurer_nid_back":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-f0589.png","treasurer_iscore_status":"","treasurer_iscore_result":0,"ceo_full_name":"ceo2","ceo_nid":"28807141301752","ceo_nid_front":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","ceo_nid_back":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","ceo_iscore_status":"","ceo_iscore_result":0,"total_loan_amount":150000,"loan_type":"Term Loan","requested_loan_amount":150000,"loan_program":"برنامج الحد من الفقر في الجيزه","loan_program_name":"برنامج الحد من الفقر في الجيزه","interest_rate":2,"installment_period":2,"lr_document":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","fra_permit":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","bod_statement":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","oc_statement":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","terms":"<div class=\\"ql-editor read-mode\\"><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl ql-indent-8\\"><strong style=\\"color: var(--heading-color); font-size: 32px;\\">إقرار وتعهد بسداد الاقساط</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl ql-indent-8\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;بأن جميع المستندات المقدمة مني الي المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 بغرض</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;الحصول على مبلغ من المال سليمة وصحيحة ومطابقة للأصل وعلى مسئوليتي الخاصة</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;وأكون متحملا المسؤولية الجنائية&nbsp;في حالة ثبوت عدم صحتها ودون أدنى مسئولية عليهم.</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;بأن العنوان الموضح لدى المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 والخاص بمزاولة النشاط</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;والخاص بإقامتي هو العنوان المختار لي ويحق لهم ارسال الإنذارات والمكاتبات والمراسلات عليه..</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;وفي حالة تغيير محل الإقامة او مزاولة النشاط التزم بإخطارهم خلال أسبوع من تاريخ التغيير</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;بخطاب مسجل بعلم الوصول أو إنذار وفي حالة عدم إخطارهم بتغيير محل الإقامة او مزاولة</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;النشاط يحق لهم إعلاني على العنوان الموضح (السابق).</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;بعدم تسليم أي مبالغ نقدية الا لأمين خزينة المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 والحصول</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;على إيصال الاستلام من الخزينة وفي حالة تسليم أي مبالغ نقدية لأي موظف تكون المؤسسة القومية</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;لتنمية الأسرة والمجتمع \u2069 غير مسئولة عنها ويحق لهم الرجوع على بالسداد كما اقر بموافقتي على</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;غرامة التأخير المقررة بمعرفة المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 وليس لدي أي اعتراض</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;عليها ويحق لهم تحصيلها بالكامل ولا يجوز لي الطعن عليها.</strong></h3><p><br></p></div>","accept_terms":1,"doctype":"Loan Request","board_members":[{"name":"tjg5j2in5o","owner":"Administrator","creation":"2025-05-16 20:24:53.946644","modified":"2025-05-16 20:25:35.714233","modified_by":"Administrator","docstatus":0,"idx":1,"full_name":"باهر محمدي","nid":"28008089998000","mobile_no":"01111232323","ngo_occupation":"manager","nid_front":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-fadca.png","nid_back":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-f0589.png","iscore":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","iscore_result":0,"parent":"LR-40","parentfield":"board_members","parenttype":"Loan Request","doctype":"LR Board Member Details"}],"beneficiaries":[{"name":"h1i0q7vmj5","owner":"Administrator","creation":"2025-05-16 20:24:53.946644","modified":"2025-05-16 20:25:35.714233","modified_by":"Administrator","docstatus":0,"idx":1,"nid":"27707141501445","full_name":"aassdd","loan_amount":150000,"project_description":"sssssssssssssss","governorate":"Alexandria","beneficiary_doc":"27707141501445","child_doc":"88jmqbjlen","parent":"LR-40","parentfield":"beneficiaries","parenttype":"Loan Request","doctype":"LR Beneficiary"}],"extra_documents":[],"financial_investigations":[],"administrative_investigations":[],"__last_sync_on":"2025-05-16T19:42:57.657Z"}', 'action': 'Submit i-Score Data', 'cmd': 'frappe.model.workflow.apply_workflow'}
2025-05-16 23:50:01,970 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-16 23:54:00,863 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2'}
2025-05-16 23:55:16,138 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'trgft'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-16 23:55:19,337 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'trgft'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-16 23:55:30,394 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'trgft'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-18 17:46:49,343 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'eeee'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-19 13:56:25,765 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'doc': '{"name":"LR-44","owner":"Administrator","creation":"2025-05-19 11:50:55.527152","modified":"2025-05-19 11:51:15.444283","modified_by":"Administrator","docstatus":0,"idx":28,"status":"Pending i-Score","creation_date":"2024-12-11","naming_series":"LR-.#.","ngo":"test 1","ngo_name":"test 1","publication_number":"9090","license_number":"090","address":"iis","email":"<EMAIL>","publication_date":"2020-01-01","license_date":"2019-01-01","contact_full_name":"irish","contact_email":"<EMAIL>","contact_nid":"29001019990999","contact_mobile_no":"01000898989","contact_nid_front":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-fadca.png","contact_nid_back":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-f0589.png","treasurer_full_name":"tersely","treasurer_nid":"29001018889888","treasurer_nid_front":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-fadca.png","treasurer_nid_back":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-f0589.png","treasurer_iscore_status":"Failed","treasurer_iscore_result":0,"treasurer_iscore_doc":"ISC-2025-05-19-1","ceo_full_name":"ceo2","ceo_nid":"28807141301752","ceo_nid_front":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","ceo_nid_back":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","ceo_iscore_status":"Failed","ceo_iscore_result":0,"ceo_iscore_doc":"ISC-2025-05-19-2","total_loan_amount":150000,"loan_type":"Term Loan","requested_loan_amount":150000,"loan_program":"برنامج الحد من الفقر في الجيزه","loan_program_name":"برنامج الحد من الفقر في الجيزه","interest_rate":2,"installment_period":2,"lr_document":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","fra_permit":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","bod_statement":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","oc_statement":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","terms":"<div class=\\"ql-editor read-mode\\"><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl ql-indent-8\\"><strong style=\\"color: var(--heading-color); font-size: 32px;\\">إقرار وتعهد بسداد الاقساط</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl ql-indent-8\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;بأن جميع المستندات المقدمة مني الي المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 بغرض</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;الحصول على مبلغ من المال سليمة وصحيحة ومطابقة للأصل وعلى مسئوليتي الخاصة</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;وأكون متحملا المسؤولية الجنائية&nbsp;في حالة ثبوت عدم صحتها ودون أدنى مسئولية عليهم.</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;بأن العنوان الموضح لدى المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 والخاص بمزاولة النشاط</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;والخاص بإقامتي هو العنوان المختار لي ويحق لهم ارسال الإنذارات والمكاتبات والمراسلات عليه..</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;وفي حالة تغيير محل الإقامة او مزاولة النشاط التزم بإخطارهم خلال أسبوع من تاريخ التغيير</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;بخطاب مسجل بعلم الوصول أو إنذار وفي حالة عدم إخطارهم بتغيير محل الإقامة او مزاولة</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;النشاط يحق لهم إعلاني على العنوان الموضح (السابق).</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><br></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;بعدم تسليم أي مبالغ نقدية الا لأمين خزينة المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 والحصول</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;على إيصال الاستلام من الخزينة وفي حالة تسليم أي مبالغ نقدية لأي موظف تكون المؤسسة القومية</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;لتنمية الأسرة والمجتمع \u2069 غير مسئولة عنها ويحق لهم الرجوع على بالسداد كما اقر بموافقتي على</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;غرامة التأخير المقررة بمعرفة المؤسسة القومية لتنمية الأسرة والمجتمع \u2069 وليس لدي أي اعتراض</strong></h3><h3 style=\\"text-align: right;\\" class=\\"ql-direction-rtl\\"><strong style=\\"color: var(--heading-color);\\">&nbsp;&nbsp;&nbsp;&nbsp;عليها ويحق لهم تحصيلها بالكامل ولا يجوز لي الطعن عليها.</strong></h3><p><br></p></div>","accept_terms":1,"doctype":"Loan Request","extra_documents":[],"administrative_investigations":[],"financial_investigations":[],"board_members":[{"name":"f12k68e4d1","owner":"Administrator","creation":"2025-05-19 11:50:55.527152","modified":"2025-05-19 11:51:15.444283","modified_by":"Administrator","docstatus":0,"idx":1,"full_name":"باهر محمدي","nid":"28008089998000","mobile_no":"01111232323","ngo_occupation":"manager","nid_front":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-fadca.png","nid_back":"/private/files/loan-request-lr-12-رعاية-الاسرة-المصرية-f0589.png","iscore":"/private/files/loan-request-lr-1-تكافل-المصريين-58be0.png","iscore_result":0,"parent":"LR-44","parentfield":"board_members","parenttype":"Loan Request","doctype":"LR Board Member Details"}],"beneficiaries":[{"name":"4osda39p1p","owner":"Administrator","creation":"2025-05-19 11:50:55.527152","modified":"2025-05-19 11:51:15.444283","modified_by":"Administrator","docstatus":0,"idx":1,"nid":"28907161701553","full_name":"tergdccj","loan_amount":150000,"project_description":"dddddddddddddddddddddddd","governorate":"Alexandria","beneficiary_doc":"28907161701553","child_doc":"bares2k9h2","parent":"LR-44","parentfield":"beneficiaries","parenttype":"Loan Request","doctype":"LR Beneficiary"}],"__last_sync_on":"2025-05-19T10:09:04.682Z"}', 'action': 'Submit i-Score Data', 'cmd': 'frappe.model.workflow.apply_workflow'}
2025-05-20 21:20:31,450 ERROR frappe New Exception collected in error log
Site: theme.com
Form Dict: {}
2025-05-20 21:20:32,727 ERROR frappe New Exception collected in error log
Site: theme.com
Form Dict: {}
2025-05-20 21:20:34,209 ERROR frappe New Exception collected in error log
Site: theme.com
Form Dict: {}
2025-05-21 16:35:54,804 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-21 17:05:03,252 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-22 14:17:28,420 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'doc': '{"name":"Customer NGO Attachments","owner":"Administrator","creation":"2025-05-22 14:15:56.778571","modified":"2025-05-22 14:15:56.778571","modified_by":"Administrator","docstatus":0,"idx":0,"is_standard":0,"label":"Customer NGO Attachments","type":"Document Type","function":"Count","aggregate_function_based_on":"","document_type":"Customer NGO Attachments","parent_document_type":"","report_function":"Sum","is_public":0,"show_percentage_stats":1,"stats_time_interval":"Daily","filters_json":"[]","dynamic_filters_json":"[]","doctype":"Number Card","__last_sync_on":"2025-05-22T11:17:28.272Z"}', 'filters': '[]', 'cmd': 'frappe.desk.doctype.number_card.number_card.get_result'}
2025-05-22 14:23:10,369 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'doc': '{"name":"Customer NGO Attachments","owner":"Administrator","creation":"2025-05-22 14:15:56.778571","modified":"2025-05-22 14:20:02.322182","modified_by":"Administrator","docstatus":0,"idx":0,"is_standard":1,"module":"Parent NGO","label":"Customer NGO Attachments","type":"Document Type","function":"Count","aggregate_function_based_on":"","document_type":"Customer NGO Attachments","parent_document_type":"","report_function":"Sum","is_public":0,"show_percentage_stats":1,"stats_time_interval":"Daily","filters_json":"[]","dynamic_filters_json":"[]","doctype":"Number Card","__last_sync_on":"2025-05-22T11:23:10.355Z"}', 'filters': '[]', 'cmd': 'frappe.desk.doctype.number_card.number_card.get_result'}
2025-05-22 14:35:32,426 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'doc': '{"name":"Customer NGO Attachments","owner":"Administrator","creation":"2025-05-22 14:34:47.454258","modified":"2025-05-22 14:34:47.454258","modified_by":"Administrator","docstatus":0,"idx":0,"is_standard":1,"module":"Parent NGO","label":"Customer NGO Attachments","type":"Document Type","report_name":null,"method":null,"function":"Count","aggregate_function_based_on":"","document_type":"Customer NGO Attachments","parent_document_type":"","report_field":null,"report_function":"Sum","is_public":0,"filters_config":null,"show_percentage_stats":1,"stats_time_interval":"Daily","filters_json":"[]","dynamic_filters_json":"[]","color":null,"doctype":"Number Card","__last_sync_on":"2025-05-22T11:34:47.507Z"}', 'filters': '[]', 'cmd': 'frappe.desk.doctype.number_card.number_card.get_result'}
2025-05-24 15:34:04,600 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': 'Administrator', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-24 15:34:24,330 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': 'Administrator', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-24 15:34:34,910 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': 'Administrator', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-24 16:23:35,071 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'html': '<!DOCTYPE html> <html lang="en" dir="ltr"> <head>  <meta charset="utf-8">  <meta http-equiv="X-UA-Compatible" content="IE=edge">  <meta name="viewport" content="width=device-width, initial-scale=1">  <meta name="description" content="">  <meta name="author" content="">  <title>Written-off Loans</title>  <link href="http://child_ngo:8006/assets/frappe/dist/css/print.bundle.YCKHG6K5.css" rel="stylesheet">  <style>   @media screen {\r\n\t.print-format-gutter {\r\n\t\tbackground-color: #d1d8dd;\r\n\t\tpadding: 30px 0px;\r\n\t}\r\n\t.print-format {\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 8px;\r\n\t\tmax-width: 8.3in;\r\n\t\tmin-height: 11.69in;\r\n\t\tpadding: 0.75in;\r\n\t\tmargin: auto;\r\n\t\tcolor: var(--gray-900);\r\n\t}\r\n\r\n\t.print-format.landscape {\r\n\t\tmax-width: 11.69in;\r\n\t\tpadding: 0.2in;\r\n\t}\r\n\r\n\t.page-break {\r\n\t\t/* padding: 15px 0px; */\r\n\t\tborder-bottom: 1px dashed #888;\r\n\t}\r\n\r\n\t/* .page-break:first-child {\r\n\t\tpadding-top: 0px;\r\n\t} */\r\n\r\n\t.page-break:last-child {\r\n\t\tborder-bottom: 0px;\r\n\t}\r\n\r\n\t/* mozilla hack for images in table */\r\n\tbody:last-child .print-format td img {\r\n\t\twidth: 100% !important;\r\n\t}\r\n\r\n\t@media(max-width: 767px) {\r\n\t\t.print-format {\r\n\t\t\tpadding: 0.2in;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@media print {\r\n\t.print-format p {\r\n\t\tmargin-left: 1px;\r\n\t\tmargin-right: 1px;\r\n\t}\r\n}\r\n\r\n.disabled-check {\r\n\tcolor: #eee;\r\n}\r\n\r\n.data-field {\r\n\tmargin-top: 5px;\r\n\tmargin-bottom: 5px;\r\n}\r\n\r\n.data-field .value {\r\n\tword-wrap: break-word;\r\n}\r\n\r\n.important .value {\r\n\tfont-size: 120%;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.important label {\r\n\tline-height: 1.8;\r\n\tmargin: 0px;\r\n}\r\n\r\n.table {\r\n\tfont-size: inherit;\r\n\tmargin: 20px 0px;\r\n}\r\n\r\n.checkbox-options {\r\n\tcolumns: var(--checkbox-options-columns);\r\n}\r\n\r\n.square-image {\r\n\twidth: 100%;\r\n\theight: 0;\r\n\tpadding: 50% 0;\r\n\tbackground-size: contain;\r\n\t/*background-size: cover;*/\r\n\tbackground-repeat: no-repeat !important;\r\n\tbackground-position: center center;\r\n\tborder-radius: 4px;\r\n}\r\n\r\n.print-item-image {\r\n\tobject-fit: contain;\r\n}\r\n\r\n.pdf-variables,\r\n.pdf-variable,\r\n.visible-pdf {\r\n\tdisplay: none !important;\r\n}\r\n\r\n.print-format {\r\n\tfont-size: 9pt;\r\n\tfont-family: Inter, "Helvetica Neue", Helvetica, Arial, "Open Sans", sans-serif;\r\n\t-webkit-print-color-adjust:exact;\r\n}\r\n\r\n.page-break {\r\n\tpage-break-after: always;\r\n}\r\n\r\n.print-heading {\r\n\tborder-bottom: 1px solid #aaa;\r\n\tmargin-bottom: 10px;\r\n}\r\n\r\n.print-heading h2 {\r\n\tmargin: 0px;\r\n}\r\n.print-heading h4 {\r\n\tmargin-top: 5px;\r\n}\r\n\r\ntable.no-border, table.no-border td {\r\n\tborder: 0px;\r\n}\r\n\r\n.print-format label {\r\n\t/* wkhtmltopdf breaks label into multiple lines when it is inline-block */\r\n\tdisplay: block;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.print-format img {\r\n\tmax-width: 100%;\r\n}\r\n\r\n.print-format table td > .primary:first-child {\r\n\tfont-weight: bold;\r\n}\r\n\r\n.print-format td, .print-format th {\r\n\tvertical-align: top !important;\r\n\tpadding: 6px !important;\r\n}\r\n\r\n.print-format p {\r\n\tmargin: 3px 0px 3px;\r\n}\r\n\r\n.print-format table td pre {\r\n\twhite-space: normal;\r\n\tword-break: normal;\r\n}\r\n\r\ntable td div {\r\n\t\r\n\t/* needed to avoid partial cutting of text between page break in wkhtmltopdf */\r\n\tpage-break-inside: avoid !important;\r\n\t\r\n}\r\n\r\n/* hack for webkit specific browser */\r\n@media (-webkit-min-device-pixel-ratio:0) {\r\n\tthead, tfoot {\r\n\t\tdisplay: table-header-group;\r\n\t}\r\n}\r\n\r\n[document-status] {\r\n\tmargin-bottom: 5mm;\r\n}\r\n\r\n.signature-img {\r\n\tbackground: #fff;\r\n\tborder-radius: 3px;\r\n\tmargin-top: 5px;\r\n\tmax-height: 150px;\r\n}\r\n\r\n.print-format-preview [data-fieldtype="Table"] {\r\n\toverflow: auto;\r\n}\r\n.print-format {\r\n    font-size: 13px;\r\n    background: white;\r\n}\r\n\r\n.print-heading {\r\n    border-bottom: 1px solid #f4f5f6;\r\n    padding-bottom: 5px;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.print-heading h2 {\r\n    font-size: 24px;\r\n}\r\n\r\n.print-heading h2 div {\r\n    font-weight: 600;\r\n}\r\n\r\n.print-heading small {\r\n    font-size: 13px !important;\r\n    font-weight: normal;\r\n    line-height: 2.5;\r\n    color: #4c5a67;\r\n}\r\n\r\n.print-format .letter-head {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.print-format label {\r\n    font-weight: normal;\r\n    font-size: 13px;\r\n    color: #4C5A67;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .data-field {\r\n    margin-top: 0;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .value {\r\n    color: #192734;\r\n    line-height: 1.8;\r\n}\r\n\r\n.print-format .section-break:not(:last-child) {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .row:not(.section-break) {\r\n    line-height: 1.6;\r\n    margin-top: 15px !important;\r\n}\r\n\r\n.print-format .important .value {\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n}\r\n\r\n.print-format th {\r\n    color: #74808b;\r\n    font-weight: normal;\r\n    border-bottom-width: 1px !important;\r\n}\r\n\r\n.print-format .table-bordered td, .print-format .table-bordered th {\r\n    border: 1px solid #f4f5f6;\r\n}\r\n\r\n.print-format .table-bordered {\r\n    border: 1px solid #f4f5f6;\r\n}\r\n\r\n.print-format td, .print-format th {\r\n    padding: 10px !important;\r\n}\r\n\r\n.print-format .primary.compact-item {\r\n    font-weight: normal;\r\n}\r\n\r\n.print-format table td .value {\r\n    font-size: 12px;\r\n    line-height: 1.8;\r\n}\r\n  </style> </head> <body>  <div class="print-format-gutter">       <div id="footer-html" class="visible-pdf">          <p class="text-center small page-number visible-pdf">      Page <span class="page"></span> of <span class="topage"></span>     </p>    </div>       <div class="print-format landscape"         >        <h2>bl7</h2> <h2>bl7</h2> <h2>bl7</h2> <h2>bl7</h2> <table class="table table-bordered">   <thead>     <tr>                <th>Statement</th>                <th>Month</th>                <th>From the beginning of the year</th>            </tr>   </thead>   <tbody>            <tr>                    <td>Total Number of Bad Balances (individuals)</td>                    <td>0</td>                    <td>1</td>                </tr>            <tr>                    <td>Total Value of Bad Financing Balances (individuals)</td>                    <td>0</td>                    <td>150000</td>                </tr>            <tr>                    <td>Total Number of Bad Balances (grouped)</td>                    <td>0</td>                    <td>0</td>                </tr>            <tr>                    <td>Total Value of Bad Balances (grouped)</td>                    <td>0</td>                    <td>0</td>                </tr>            <tr>                    <td>Total Number Of Bad Balances For All Types Of Customers</td>                    <td>0</td>                    <td>1</td>                </tr>            <tr>                    <td>Total Value Of Bad Balances For All Types Of Customers</td>                    <td>0</td>                    <td>150000</td>                </tr>        </tbody> </table>   </div>  </div> </body> </html> ', 'orientation': 'Landscape', 'cmd': 'frappe.utils.print_format.report_to_pdf'}
2025-05-24 16:23:44,257 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'html': '<!DOCTYPE html> <html lang="en" dir="ltr"> <head>  <meta charset="utf-8">  <meta http-equiv="X-UA-Compatible" content="IE=edge">  <meta name="viewport" content="width=device-width, initial-scale=1">  <meta name="description" content="">  <meta name="author" content="">  <title>Written-off Loans</title>  <link href="http://child_ngo:8006/assets/frappe/dist/css/print.bundle.YCKHG6K5.css" rel="stylesheet">  <style>   @media screen {\r\n\t.print-format-gutter {\r\n\t\tbackground-color: #d1d8dd;\r\n\t\tpadding: 30px 0px;\r\n\t}\r\n\t.print-format {\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 8px;\r\n\t\tmax-width: 8.3in;\r\n\t\tmin-height: 11.69in;\r\n\t\tpadding: 0.75in;\r\n\t\tmargin: auto;\r\n\t\tcolor: var(--gray-900);\r\n\t}\r\n\r\n\t.print-format.landscape {\r\n\t\tmax-width: 11.69in;\r\n\t\tpadding: 0.2in;\r\n\t}\r\n\r\n\t.page-break {\r\n\t\t/* padding: 15px 0px; */\r\n\t\tborder-bottom: 1px dashed #888;\r\n\t}\r\n\r\n\t/* .page-break:first-child {\r\n\t\tpadding-top: 0px;\r\n\t} */\r\n\r\n\t.page-break:last-child {\r\n\t\tborder-bottom: 0px;\r\n\t}\r\n\r\n\t/* mozilla hack for images in table */\r\n\tbody:last-child .print-format td img {\r\n\t\twidth: 100% !important;\r\n\t}\r\n\r\n\t@media(max-width: 767px) {\r\n\t\t.print-format {\r\n\t\t\tpadding: 0.2in;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@media print {\r\n\t.print-format p {\r\n\t\tmargin-left: 1px;\r\n\t\tmargin-right: 1px;\r\n\t}\r\n}\r\n\r\n.disabled-check {\r\n\tcolor: #eee;\r\n}\r\n\r\n.data-field {\r\n\tmargin-top: 5px;\r\n\tmargin-bottom: 5px;\r\n}\r\n\r\n.data-field .value {\r\n\tword-wrap: break-word;\r\n}\r\n\r\n.important .value {\r\n\tfont-size: 120%;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.important label {\r\n\tline-height: 1.8;\r\n\tmargin: 0px;\r\n}\r\n\r\n.table {\r\n\tfont-size: inherit;\r\n\tmargin: 20px 0px;\r\n}\r\n\r\n.checkbox-options {\r\n\tcolumns: var(--checkbox-options-columns);\r\n}\r\n\r\n.square-image {\r\n\twidth: 100%;\r\n\theight: 0;\r\n\tpadding: 50% 0;\r\n\tbackground-size: contain;\r\n\t/*background-size: cover;*/\r\n\tbackground-repeat: no-repeat !important;\r\n\tbackground-position: center center;\r\n\tborder-radius: 4px;\r\n}\r\n\r\n.print-item-image {\r\n\tobject-fit: contain;\r\n}\r\n\r\n.pdf-variables,\r\n.pdf-variable,\r\n.visible-pdf {\r\n\tdisplay: none !important;\r\n}\r\n\r\n.print-format {\r\n\tfont-size: 9pt;\r\n\tfont-family: Inter, "Helvetica Neue", Helvetica, Arial, "Open Sans", sans-serif;\r\n\t-webkit-print-color-adjust:exact;\r\n}\r\n\r\n.page-break {\r\n\tpage-break-after: always;\r\n}\r\n\r\n.print-heading {\r\n\tborder-bottom: 1px solid #aaa;\r\n\tmargin-bottom: 10px;\r\n}\r\n\r\n.print-heading h2 {\r\n\tmargin: 0px;\r\n}\r\n.print-heading h4 {\r\n\tmargin-top: 5px;\r\n}\r\n\r\ntable.no-border, table.no-border td {\r\n\tborder: 0px;\r\n}\r\n\r\n.print-format label {\r\n\t/* wkhtmltopdf breaks label into multiple lines when it is inline-block */\r\n\tdisplay: block;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.print-format img {\r\n\tmax-width: 100%;\r\n}\r\n\r\n.print-format table td > .primary:first-child {\r\n\tfont-weight: bold;\r\n}\r\n\r\n.print-format td, .print-format th {\r\n\tvertical-align: top !important;\r\n\tpadding: 6px !important;\r\n}\r\n\r\n.print-format p {\r\n\tmargin: 3px 0px 3px;\r\n}\r\n\r\n.print-format table td pre {\r\n\twhite-space: normal;\r\n\tword-break: normal;\r\n}\r\n\r\ntable td div {\r\n\t\r\n\t/* needed to avoid partial cutting of text between page break in wkhtmltopdf */\r\n\tpage-break-inside: avoid !important;\r\n\t\r\n}\r\n\r\n/* hack for webkit specific browser */\r\n@media (-webkit-min-device-pixel-ratio:0) {\r\n\tthead, tfoot {\r\n\t\tdisplay: table-header-group;\r\n\t}\r\n}\r\n\r\n[document-status] {\r\n\tmargin-bottom: 5mm;\r\n}\r\n\r\n.signature-img {\r\n\tbackground: #fff;\r\n\tborder-radius: 3px;\r\n\tmargin-top: 5px;\r\n\tmax-height: 150px;\r\n}\r\n\r\n.print-format-preview [data-fieldtype="Table"] {\r\n\toverflow: auto;\r\n}\r\n.print-format {\r\n    font-size: 13px;\r\n    background: white;\r\n}\r\n\r\n.print-heading {\r\n    border-bottom: 1px solid #f4f5f6;\r\n    padding-bottom: 5px;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.print-heading h2 {\r\n    font-size: 24px;\r\n}\r\n\r\n.print-heading h2 div {\r\n    font-weight: 600;\r\n}\r\n\r\n.print-heading small {\r\n    font-size: 13px !important;\r\n    font-weight: normal;\r\n    line-height: 2.5;\r\n    color: #4c5a67;\r\n}\r\n\r\n.print-format .letter-head {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.print-format label {\r\n    font-weight: normal;\r\n    font-size: 13px;\r\n    color: #4C5A67;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .data-field {\r\n    margin-top: 0;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .value {\r\n    color: #192734;\r\n    line-height: 1.8;\r\n}\r\n\r\n.print-format .section-break:not(:last-child) {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .row:not(.section-break) {\r\n    line-height: 1.6;\r\n    margin-top: 15px !important;\r\n}\r\n\r\n.print-format .important .value {\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n}\r\n\r\n.print-format th {\r\n    color: #74808b;\r\n    font-weight: normal;\r\n    border-bottom-width: 1px !important;\r\n}\r\n\r\n.print-format .table-bordered td, .print-format .table-bordered th {\r\n    border: 1px solid #f4f5f6;\r\n}\r\n\r\n.print-format .table-bordered {\r\n    border: 1px solid #f4f5f6;\r\n}\r\n\r\n.print-format td, .print-format th {\r\n    padding: 10px !important;\r\n}\r\n\r\n.print-format .primary.compact-item {\r\n    font-weight: normal;\r\n}\r\n\r\n.print-format table td .value {\r\n    font-size: 12px;\r\n    line-height: 1.8;\r\n}\r\n  </style> </head> <body>  <div class="print-format-gutter">       <div id="footer-html" class="visible-pdf">          <p class="text-center small page-number visible-pdf">      Page <span class="page"></span> of <span class="topage"></span>     </p>    </div>       <div class="print-format landscape"         >        <h2>bl7</h2> <h2>bl7</h2> <h2>bl7</h2> <h2>bl7</h2> <table class="table table-bordered">   <thead>     <tr>                <th>Statement</th>                <th>Month</th>                <th>From the beginning of the year</th>            </tr>   </thead>   <tbody>            <tr>                    <td>Total Number of Bad Balances (individuals)</td>                    <td>0</td>                    <td>1</td>                </tr>            <tr>                    <td>Total Value of Bad Financing Balances (individuals)</td>                    <td>0</td>                    <td>150000</td>                </tr>            <tr>                    <td>Total Number of Bad Balances (grouped)</td>                    <td>0</td>                    <td>0</td>                </tr>            <tr>                    <td>Total Value of Bad Balances (grouped)</td>                    <td>0</td>                    <td>0</td>                </tr>            <tr>                    <td>Total Number Of Bad Balances For All Types Of Customers</td>                    <td>0</td>                    <td>1</td>                </tr>            <tr>                    <td>Total Value Of Bad Balances For All Types Of Customers</td>                    <td>0</td>                    <td>150000</td>                </tr>        </tbody> </table>   </div>  </div> </body> </html> ', 'orientation': 'Landscape', 'cmd': 'frappe.utils.print_format.report_to_pdf'}
2025-05-24 16:23:52,107 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'html': '<!DOCTYPE html> <html lang="en" dir="ltr"> <head>  <meta charset="utf-8">  <meta http-equiv="X-UA-Compatible" content="IE=edge">  <meta name="viewport" content="width=device-width, initial-scale=1">  <meta name="description" content="">  <meta name="author" content="">  <title>Written-off Loans</title>  <link href="http://child_ngo:8006/assets/frappe/dist/css/print.bundle.YCKHG6K5.css" rel="stylesheet">  <style>   @media screen {\r\n\t.print-format-gutter {\r\n\t\tbackground-color: #d1d8dd;\r\n\t\tpadding: 30px 0px;\r\n\t}\r\n\t.print-format {\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 8px;\r\n\t\tmax-width: 8.3in;\r\n\t\tmin-height: 11.69in;\r\n\t\tpadding: 0.75in;\r\n\t\tmargin: auto;\r\n\t\tcolor: var(--gray-900);\r\n\t}\r\n\r\n\t.print-format.landscape {\r\n\t\tmax-width: 11.69in;\r\n\t\tpadding: 0.2in;\r\n\t}\r\n\r\n\t.page-break {\r\n\t\t/* padding: 15px 0px; */\r\n\t\tborder-bottom: 1px dashed #888;\r\n\t}\r\n\r\n\t/* .page-break:first-child {\r\n\t\tpadding-top: 0px;\r\n\t} */\r\n\r\n\t.page-break:last-child {\r\n\t\tborder-bottom: 0px;\r\n\t}\r\n\r\n\t/* mozilla hack for images in table */\r\n\tbody:last-child .print-format td img {\r\n\t\twidth: 100% !important;\r\n\t}\r\n\r\n\t@media(max-width: 767px) {\r\n\t\t.print-format {\r\n\t\t\tpadding: 0.2in;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@media print {\r\n\t.print-format p {\r\n\t\tmargin-left: 1px;\r\n\t\tmargin-right: 1px;\r\n\t}\r\n}\r\n\r\n.disabled-check {\r\n\tcolor: #eee;\r\n}\r\n\r\n.data-field {\r\n\tmargin-top: 5px;\r\n\tmargin-bottom: 5px;\r\n}\r\n\r\n.data-field .value {\r\n\tword-wrap: break-word;\r\n}\r\n\r\n.important .value {\r\n\tfont-size: 120%;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.important label {\r\n\tline-height: 1.8;\r\n\tmargin: 0px;\r\n}\r\n\r\n.table {\r\n\tfont-size: inherit;\r\n\tmargin: 20px 0px;\r\n}\r\n\r\n.checkbox-options {\r\n\tcolumns: var(--checkbox-options-columns);\r\n}\r\n\r\n.square-image {\r\n\twidth: 100%;\r\n\theight: 0;\r\n\tpadding: 50% 0;\r\n\tbackground-size: contain;\r\n\t/*background-size: cover;*/\r\n\tbackground-repeat: no-repeat !important;\r\n\tbackground-position: center center;\r\n\tborder-radius: 4px;\r\n}\r\n\r\n.print-item-image {\r\n\tobject-fit: contain;\r\n}\r\n\r\n.pdf-variables,\r\n.pdf-variable,\r\n.visible-pdf {\r\n\tdisplay: none !important;\r\n}\r\n\r\n.print-format {\r\n\tfont-size: 9pt;\r\n\tfont-family: Inter, "Helvetica Neue", Helvetica, Arial, "Open Sans", sans-serif;\r\n\t-webkit-print-color-adjust:exact;\r\n}\r\n\r\n.page-break {\r\n\tpage-break-after: always;\r\n}\r\n\r\n.print-heading {\r\n\tborder-bottom: 1px solid #aaa;\r\n\tmargin-bottom: 10px;\r\n}\r\n\r\n.print-heading h2 {\r\n\tmargin: 0px;\r\n}\r\n.print-heading h4 {\r\n\tmargin-top: 5px;\r\n}\r\n\r\ntable.no-border, table.no-border td {\r\n\tborder: 0px;\r\n}\r\n\r\n.print-format label {\r\n\t/* wkhtmltopdf breaks label into multiple lines when it is inline-block */\r\n\tdisplay: block;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.print-format img {\r\n\tmax-width: 100%;\r\n}\r\n\r\n.print-format table td > .primary:first-child {\r\n\tfont-weight: bold;\r\n}\r\n\r\n.print-format td, .print-format th {\r\n\tvertical-align: top !important;\r\n\tpadding: 6px !important;\r\n}\r\n\r\n.print-format p {\r\n\tmargin: 3px 0px 3px;\r\n}\r\n\r\n.print-format table td pre {\r\n\twhite-space: normal;\r\n\tword-break: normal;\r\n}\r\n\r\ntable td div {\r\n\t\r\n\t/* needed to avoid partial cutting of text between page break in wkhtmltopdf */\r\n\tpage-break-inside: avoid !important;\r\n\t\r\n}\r\n\r\n/* hack for webkit specific browser */\r\n@media (-webkit-min-device-pixel-ratio:0) {\r\n\tthead, tfoot {\r\n\t\tdisplay: table-header-group;\r\n\t}\r\n}\r\n\r\n[document-status] {\r\n\tmargin-bottom: 5mm;\r\n}\r\n\r\n.signature-img {\r\n\tbackground: #fff;\r\n\tborder-radius: 3px;\r\n\tmargin-top: 5px;\r\n\tmax-height: 150px;\r\n}\r\n\r\n.print-format-preview [data-fieldtype="Table"] {\r\n\toverflow: auto;\r\n}\r\n.print-format {\r\n    font-size: 13px;\r\n    background: white;\r\n}\r\n\r\n.print-heading {\r\n    border-bottom: 1px solid #f4f5f6;\r\n    padding-bottom: 5px;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.print-heading h2 {\r\n    font-size: 24px;\r\n}\r\n\r\n.print-heading h2 div {\r\n    font-weight: 600;\r\n}\r\n\r\n.print-heading small {\r\n    font-size: 13px !important;\r\n    font-weight: normal;\r\n    line-height: 2.5;\r\n    color: #4c5a67;\r\n}\r\n\r\n.print-format .letter-head {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.print-format label {\r\n    font-weight: normal;\r\n    font-size: 13px;\r\n    color: #4C5A67;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .data-field {\r\n    margin-top: 0;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .value {\r\n    color: #192734;\r\n    line-height: 1.8;\r\n}\r\n\r\n.print-format .section-break:not(:last-child) {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .row:not(.section-break) {\r\n    line-height: 1.6;\r\n    margin-top: 15px !important;\r\n}\r\n\r\n.print-format .important .value {\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n}\r\n\r\n.print-format th {\r\n    color: #74808b;\r\n    font-weight: normal;\r\n    border-bottom-width: 1px !important;\r\n}\r\n\r\n.print-format .table-bordered td, .print-format .table-bordered th {\r\n    border: 1px solid #f4f5f6;\r\n}\r\n\r\n.print-format .table-bordered {\r\n    border: 1px solid #f4f5f6;\r\n}\r\n\r\n.print-format td, .print-format th {\r\n    padding: 10px !important;\r\n}\r\n\r\n.print-format .primary.compact-item {\r\n    font-weight: normal;\r\n}\r\n\r\n.print-format table td .value {\r\n    font-size: 12px;\r\n    line-height: 1.8;\r\n}\r\n  </style> </head> <body>  <div class="print-format-gutter">       <div id="footer-html" class="visible-pdf">          <p class="text-center small page-number visible-pdf">      Page <span class="page"></span> of <span class="topage"></span>     </p>    </div>       <div class="print-format landscape"         >        <h2>bl7</h2> <h2>bl7</h2> <h2>bl7</h2> <h2>bl7</h2> <table class="table table-bordered">   <thead>     <tr>                <th>Statement</th>                <th>Month</th>                <th>From the beginning of the year</th>            </tr>   </thead>   <tbody>            <tr>                    <td>Total Number of Bad Balances (individuals)</td>                    <td>0</td>                    <td>1</td>                </tr>            <tr>                    <td>Total Value of Bad Financing Balances (individuals)</td>                    <td>0</td>                    <td>150000</td>                </tr>            <tr>                    <td>Total Number of Bad Balances (grouped)</td>                    <td>0</td>                    <td>0</td>                </tr>            <tr>                    <td>Total Value of Bad Balances (grouped)</td>                    <td>0</td>                    <td>0</td>                </tr>            <tr>                    <td>Total Number Of Bad Balances For All Types Of Customers</td>                    <td>0</td>                    <td>1</td>                </tr>            <tr>                    <td>Total Value Of Bad Balances For All Types Of Customers</td>                    <td>0</td>                    <td>150000</td>                </tr>        </tbody> </table>   </div>  </div> </body> </html> ', 'orientation': 'Landscape', 'cmd': 'frappe.utils.print_format.report_to_pdf'}
2025-05-24 16:24:07,792 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'html': '<!DOCTYPE html> <html lang="en" dir="ltr"> <head>  <meta charset="utf-8">  <meta http-equiv="X-UA-Compatible" content="IE=edge">  <meta name="viewport" content="width=device-width, initial-scale=1">  <meta name="description" content="">  <meta name="author" content="">  <title>Written-off Loans</title>  <link href="http://child_ngo:8006/assets/frappe/dist/css/print.bundle.YCKHG6K5.css" rel="stylesheet">  <style>   @media screen {\r\n\t.print-format-gutter {\r\n\t\tbackground-color: #d1d8dd;\r\n\t\tpadding: 30px 0px;\r\n\t}\r\n\t.print-format {\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 8px;\r\n\t\tmax-width: 8.3in;\r\n\t\tmin-height: 11.69in;\r\n\t\tpadding: 0.75in;\r\n\t\tmargin: auto;\r\n\t\tcolor: var(--gray-900);\r\n\t}\r\n\r\n\t.print-format.landscape {\r\n\t\tmax-width: 11.69in;\r\n\t\tpadding: 0.2in;\r\n\t}\r\n\r\n\t.page-break {\r\n\t\t/* padding: 15px 0px; */\r\n\t\tborder-bottom: 1px dashed #888;\r\n\t}\r\n\r\n\t/* .page-break:first-child {\r\n\t\tpadding-top: 0px;\r\n\t} */\r\n\r\n\t.page-break:last-child {\r\n\t\tborder-bottom: 0px;\r\n\t}\r\n\r\n\t/* mozilla hack for images in table */\r\n\tbody:last-child .print-format td img {\r\n\t\twidth: 100% !important;\r\n\t}\r\n\r\n\t@media(max-width: 767px) {\r\n\t\t.print-format {\r\n\t\t\tpadding: 0.2in;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@media print {\r\n\t.print-format p {\r\n\t\tmargin-left: 1px;\r\n\t\tmargin-right: 1px;\r\n\t}\r\n}\r\n\r\n.disabled-check {\r\n\tcolor: #eee;\r\n}\r\n\r\n.data-field {\r\n\tmargin-top: 5px;\r\n\tmargin-bottom: 5px;\r\n}\r\n\r\n.data-field .value {\r\n\tword-wrap: break-word;\r\n}\r\n\r\n.important .value {\r\n\tfont-size: 120%;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.important label {\r\n\tline-height: 1.8;\r\n\tmargin: 0px;\r\n}\r\n\r\n.table {\r\n\tfont-size: inherit;\r\n\tmargin: 20px 0px;\r\n}\r\n\r\n.checkbox-options {\r\n\tcolumns: var(--checkbox-options-columns);\r\n}\r\n\r\n.square-image {\r\n\twidth: 100%;\r\n\theight: 0;\r\n\tpadding: 50% 0;\r\n\tbackground-size: contain;\r\n\t/*background-size: cover;*/\r\n\tbackground-repeat: no-repeat !important;\r\n\tbackground-position: center center;\r\n\tborder-radius: 4px;\r\n}\r\n\r\n.print-item-image {\r\n\tobject-fit: contain;\r\n}\r\n\r\n.pdf-variables,\r\n.pdf-variable,\r\n.visible-pdf {\r\n\tdisplay: none !important;\r\n}\r\n\r\n.print-format {\r\n\tfont-size: 9pt;\r\n\tfont-family: Inter, "Helvetica Neue", Helvetica, Arial, "Open Sans", sans-serif;\r\n\t-webkit-print-color-adjust:exact;\r\n}\r\n\r\n.page-break {\r\n\tpage-break-after: always;\r\n}\r\n\r\n.print-heading {\r\n\tborder-bottom: 1px solid #aaa;\r\n\tmargin-bottom: 10px;\r\n}\r\n\r\n.print-heading h2 {\r\n\tmargin: 0px;\r\n}\r\n.print-heading h4 {\r\n\tmargin-top: 5px;\r\n}\r\n\r\ntable.no-border, table.no-border td {\r\n\tborder: 0px;\r\n}\r\n\r\n.print-format label {\r\n\t/* wkhtmltopdf breaks label into multiple lines when it is inline-block */\r\n\tdisplay: block;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.print-format img {\r\n\tmax-width: 100%;\r\n}\r\n\r\n.print-format table td > .primary:first-child {\r\n\tfont-weight: bold;\r\n}\r\n\r\n.print-format td, .print-format th {\r\n\tvertical-align: top !important;\r\n\tpadding: 6px !important;\r\n}\r\n\r\n.print-format p {\r\n\tmargin: 3px 0px 3px;\r\n}\r\n\r\n.print-format table td pre {\r\n\twhite-space: normal;\r\n\tword-break: normal;\r\n}\r\n\r\ntable td div {\r\n\t\r\n\t/* needed to avoid partial cutting of text between page break in wkhtmltopdf */\r\n\tpage-break-inside: avoid !important;\r\n\t\r\n}\r\n\r\n/* hack for webkit specific browser */\r\n@media (-webkit-min-device-pixel-ratio:0) {\r\n\tthead, tfoot {\r\n\t\tdisplay: table-header-group;\r\n\t}\r\n}\r\n\r\n[document-status] {\r\n\tmargin-bottom: 5mm;\r\n}\r\n\r\n.signature-img {\r\n\tbackground: #fff;\r\n\tborder-radius: 3px;\r\n\tmargin-top: 5px;\r\n\tmax-height: 150px;\r\n}\r\n\r\n.print-format-preview [data-fieldtype="Table"] {\r\n\toverflow: auto;\r\n}\r\n.print-format {\r\n    font-size: 13px;\r\n    background: white;\r\n}\r\n\r\n.print-heading {\r\n    border-bottom: 1px solid #f4f5f6;\r\n    padding-bottom: 5px;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.print-heading h2 {\r\n    font-size: 24px;\r\n}\r\n\r\n.print-heading h2 div {\r\n    font-weight: 600;\r\n}\r\n\r\n.print-heading small {\r\n    font-size: 13px !important;\r\n    font-weight: normal;\r\n    line-height: 2.5;\r\n    color: #4c5a67;\r\n}\r\n\r\n.print-format .letter-head {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.print-format label {\r\n    font-weight: normal;\r\n    font-size: 13px;\r\n    color: #4C5A67;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .data-field {\r\n    margin-top: 0;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .value {\r\n    color: #192734;\r\n    line-height: 1.8;\r\n}\r\n\r\n.print-format .section-break:not(:last-child) {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.print-format .row:not(.section-break) {\r\n    line-height: 1.6;\r\n    margin-top: 15px !important;\r\n}\r\n\r\n.print-format .important .value {\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n}\r\n\r\n.print-format th {\r\n    color: #74808b;\r\n    font-weight: normal;\r\n    border-bottom-width: 1px !important;\r\n}\r\n\r\n.print-format .table-bordered td, .print-format .table-bordered th {\r\n    border: 1px solid #f4f5f6;\r\n}\r\n\r\n.print-format .table-bordered {\r\n    border: 1px solid #f4f5f6;\r\n}\r\n\r\n.print-format td, .print-format th {\r\n    padding: 10px !important;\r\n}\r\n\r\n.print-format .primary.compact-item {\r\n    font-weight: normal;\r\n}\r\n\r\n.print-format table td .value {\r\n    font-size: 12px;\r\n    line-height: 1.8;\r\n}\r\n  </style> </head> <body>  <div class="print-format-gutter">       <div id="footer-html" class="visible-pdf">          <p class="text-center small page-number visible-pdf">      Page <span class="page"></span> of <span class="topage"></span>     </p>    </div>       <div class="print-format landscape"         >        <h2>bl7</h2> <h2>bl7</h2> <h2>bl7</h2> <h2>bl7</h2> <table class="table table-bordered">   <thead>     <tr>                <th>Statement</th>                <th>Month</th>                <th>From the beginning of the year</th>            </tr>   </thead>   <tbody>            <tr>                    <td>Total Number of Bad Balances (individuals)</td>                    <td>0</td>                    <td>1</td>                </tr>            <tr>                    <td>Total Value of Bad Financing Balances (individuals)</td>                    <td>0</td>                    <td>150000</td>                </tr>            <tr>                    <td>Total Number of Bad Balances (grouped)</td>                    <td>0</td>                    <td>0</td>                </tr>            <tr>                    <td>Total Value of Bad Balances (grouped)</td>                    <td>0</td>                    <td>0</td>                </tr>            <tr>                    <td>Total Number Of Bad Balances For All Types Of Customers</td>                    <td>0</td>                    <td>1</td>                </tr>            <tr>                    <td>Total Value Of Bad Balances For All Types Of Customers</td>                    <td>0</td>                    <td>150000</td>                </tr>        </tbody> </table>   </div>  </div> </body> </html> ', 'orientation': 'Landscape', 'cmd': 'frappe.utils.print_format.report_to_pdf'}
2025-05-24 16:30:14,237 ERROR frappe Error while inserting deferred Error Log record: Error Log 7cv52a8a7c: 'Title' (wkhtmltopdf reported an error:
The switch --print-media-type, is not support using unpatched qt, and will be ignored.The switch --footer-html, is not support using unpatched qt, and will be ignored.The switch --disable-smart-shrinking, is not support using unpatched qt, and will be ignored.Warning: Ignoring XDG_SESSION_TYPE=wayland on Gnome. Use QT_QPA_PLATFORM=wayland to run on Wayland anyway.
QNetworkReplyImplPrivate::error: Internal problem, this method must only be called once.
Exit with code 1 due to network error: OperationCanceledError
) will get truncated, as max characters allowed is 140
Site: child_ngo
Form Dict: {}
2025-05-24 16:30:14,247 ERROR frappe Error while inserting deferred Error Log record: Error Log ssg4akpt7d: 'Title' (wkhtmltopdf reported an error:
The switch --print-media-type, is not support using unpatched qt, and will be ignored.The switch --footer-html, is not support using unpatched qt, and will be ignored.The switch --disable-smart-shrinking, is not support using unpatched qt, and will be ignored.Warning: Ignoring XDG_SESSION_TYPE=wayland on Gnome. Use QT_QPA_PLATFORM=wayland to run on Wayland anyway.
QNetworkReplyImplPrivate::error: Internal problem, this method must only be called once.
Exit with code 1 due to network error: OperationCanceledError
) will get truncated, as max characters allowed is 140
Site: child_ngo
Form Dict: {}
2025-05-24 16:30:14,254 ERROR frappe Error while inserting deferred Error Log record: Error Log 3qf9c5mmu6: 'Title' (wkhtmltopdf reported an error:
The switch --print-media-type, is not support using unpatched qt, and will be ignored.The switch --footer-html, is not support using unpatched qt, and will be ignored.The switch --disable-smart-shrinking, is not support using unpatched qt, and will be ignored.Warning: Ignoring XDG_SESSION_TYPE=wayland on Gnome. Use QT_QPA_PLATFORM=wayland to run on Wayland anyway.
QNetworkReplyImplPrivate::error: Internal problem, this method must only be called once.
Exit with code 1 due to network error: OperationCanceledError
) will get truncated, as max characters allowed is 140
Site: child_ngo
Form Dict: {}
2025-05-24 16:30:14,259 ERROR frappe Error while inserting deferred Error Log record: Error Log gnr428csd1: 'Title' (wkhtmltopdf reported an error:
The switch --print-media-type, is not support using unpatched qt, and will be ignored.The switch --footer-html, is not support using unpatched qt, and will be ignored.The switch --disable-smart-shrinking, is not support using unpatched qt, and will be ignored.Warning: Ignoring XDG_SESSION_TYPE=wayland on Gnome. Use QT_QPA_PLATFORM=wayland to run on Wayland anyway.
QNetworkReplyImplPrivate::error: Internal problem, this method must only be called once.
Exit with code 1 due to network error: OperationCanceledError
) will get truncated, as max characters allowed is 140
Site: child_ngo
Form Dict: {}
2025-05-25 11:10:25,046 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 11:10:29,816 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/v1/orders/urls.py", line 3, in <module>
    order
NameError: name 'order' is not defined
2025-05-25 11:10:30,130 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 11:10:31,764 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/v1/orders/urls.py", line 3, in <module>
    order
NameError: name 'order' is not defined
2025-05-25 11:10:31,833 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 11:10:33,204 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/v1/orders/urls.py", line 3, in <module>
    order
NameError: name 'order' is not defined
2025-05-25 11:12:55,540 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 11:12:56,731 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
ModuleNotFoundError: No module named 'nexus.api.v1.orders.urls'
2025-05-25 11:12:56,815 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 11:12:58,135 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
ModuleNotFoundError: No module named 'nexus.api.v1.orders.urls'
2025-05-25 11:13:48,911 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'page': '1', 'per_page': '20', 'sort_by': 'issue_date', 'sort_order': 'desc'}
2025-05-25 11:19:54,707 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'page': '1', 'per_page': '20', 'sort_by': 'issue_date', 'sort_order': 'asc'}
2025-05-25 11:19:55,482 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'page': '1', 'per_page': '20', 'sort_by': 'issue_date', 'sort_order': 'desc'}
2025-05-25 12:39:52,913 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 12:39:53,375 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
ModuleNotFoundError: No module named 'nexus.api.v1.orders.urls'
2025-05-25 12:39:53,502 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
