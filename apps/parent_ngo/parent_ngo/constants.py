ERRORS = {
	"invalid_beneficiary_nid": "Beneficiary National ID must be 14 characters long",
	"invalid_contact_nid": "Contact Person National ID must be 14 digits long",
	"invalid_treasurer_nid": "Treasurer National ID must be 14 digits long",
	"invalid_ceo_nid": "CEO’s National ID must be 14 digits",
	"invalid_board_member_nid": "Board Member National ID in row {0} must be 14 digits long",
	"invalid_contact_mobile_no": "Contact Person Mobile number must be 11 characters long & has a valid prefix",
	"invalid_treasurer_mobile_no": "Treasurer Mobile number must be 11 characters long & has a valid prefix",
	"invalid_ceo_mobile_no": "CEO’s mobile number must be 11 digits with a valid prefix",
	"invalid_board_member_mobile_no": "Board Member Mobile number in row {0} must be 11 characters long & has a valid prefix",
	"invalid_amount_criteria": "Maixmum Loan Amount must be greater than or equal to Minimum Loan AMount",
	"invalid_fund_pool": "Fund Pool must be greater than or equal to Sanctioned Funds",
	"missing_contact_nid_front": "Contact Person front national ID attachment is mandatory",
	"missing_contact_nid_back": "Contact Person back national ID attachment is mandatory",
	"missing_treasurer_nid_front": "Treasurer front national ID attachment is mandatory",
	"missing_treasurer_nid_back": "Treasurer back national ID attachment is mandatory",
	"missing_ceo_nid_front": "Please upload the front side of the CEO’s National ID",
	"missing_ceo_nid_back": "Please upload the back side of the CEO’s National ID",
	"missing_board_member_nid_front": "Board Member front national ID attachment in row {0} is mandatory",
	"missing_board_member_nid_back": "Board Member back national ID attachment in row {0} is mandatory",
	"missing_board_member_iscore": "Board Member i-Score attachment in row {0} is mandatory",
	"duplicate_beneficiaries": "Beneficiary in row {0} is duplicate",
	"beneficiary_existing_active_loans": "Beneficiary in row {0} has current active loans or loan requests in the same loan program",
	"invalid_lr_beneficiary_nid": "Beneficiary National ID in row {0} must be 14 characters long",
	"invalid_loan_program_criteria": "The requested loan amount doesn't comply with the chosen loan program criteria",
	"missing_lr_document": "Loan Request Document attachment is mandatory",
	"missing_fra_permit": "FRA Permit attachment is mandatory",
	"missing_bod_statement": "Board of Directors Statement attachment is mandatory",
	"missing_oc_statement": "Organizational Chart Statement attachment is mandatory",
	"missing_hc_report": "Higher Committee Report attachment is mandatory",
	"invalid_accept_terms": "Terms & Conditions must be accepted before proceeding",
	"not_saved_doc": "The document has not been saved. Please save it and try again.",
	"missing_mandatory_field": "{0} field is mandatory",
	"mandatory_lending_end_date": "Lending End Date in row {0} of {1} is mandatory",
	"duplicate_financial_investigation": "There is already opened or in progress financial investigation for this loan request.",
	"duplicate_administrative_investigation": "There is already opened or in progress administrative investigation for this loan request.",
	"incomplete_financial_investigations": "Please fill in all required fields for financial investigations. Financial investigations should at least have one Completed investigation and none Opened or In Progress investigations.",
	"incomplete_administrative_investigations": "Please fill in all required fields for administrative investigations. Administrative investigations should at least have one Completed investigation and none Opened or In Progress investigations.",
	"invalid_directorate_nid": "Directorate National ID must be 14 digits long",
	"mandatory_ownership_date": "Ownership Date is mandatory",
	"mandatory_allocation_date": "Allocation Date is mandatory",
	"mandatory_rental_dates": "Rental Start Date and Rental End Date are mandatory",
	"invalid_rental_dates": "Rental Start Date must be before Rental End Date",
	"invalid_investigation_delete_status": "The investigation cannot be deleted when its status is completed",
	"inconsistent_totals": "Loan totals are inconsistent. Please make sure that the totals match the repayment schedule",
	"inconsistent_remaining_payments": "Remaining payments are inconsistent. Please make sure that the remaining payments match the repayment schedule",
	"invalid_disbursement_amount": "Disbursement amount cannot be greater than the remaining loan amount",
	"invalid_zero_disbursement_amount": "Disbursement amount cannot be zero",
	"required_payment_repayment_date": "Repayment Date is mandatory for payments",
	"required_payment_due_date": "Due Date is mandatory for payments",
	"inconsistent_payment_amounts": "Payment amounts are inconsistent. Please make sure that the payment amounts match the repayment schedule",
	"mandatory_beneficiary_received_loan": "Received Loan field for the Beneficiary in row {0} is mandatory",
	"invalid_loan_totals": "Total amount paid against this loan can't exceed the total disbursed amount",
	"duplicate_financial_follow_up": "There is already opened or in progress financial follow up for this loan.",
	"duplicate_administrative_follow_up": "There is already opened or in progress administrative follow up for this loan",
	"invalid_follow_up_delete_status": "The follow up cannot be deleted when its status is completed",
	"insufficient_funds": "The loan program fund pool has insufficient funds for the requested loan amount",
    "invalid_lending_end_date": "Lending End Date in row {0} of {1} must be a date after Project Start Date",
    "missing_required_attachments": "Required attachments are missing:<br> {0}",
    "incomplete_iscore_data": "Please fill in all required fields for i-SCORE data",
    "invalid_ngo_activities_rating": "NGO Activities Rating must be between {0} and {1} based on the Lending Status",
}

MESSAGES = {
	"welcome_email_sent": "Welcome email has been sent successfully",
	"reset_password_email_sent": "Reset password email has been sent successfully"
}
