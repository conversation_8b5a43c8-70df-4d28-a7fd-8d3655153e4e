import frappe
import iam, parent_ngo
from parent_ngo.utils.validate.administrative_investigation import validate_administrative_investigation_exists
from parent_ngo.utils.loan_request import get_loan_request_data
from iam.utils.table import add_to_table
from iam.utils.file import upload_files

def get_administrative_investigation_data(**parameters):
    
    administrative_investigation_details = iam.db.get(**parent_ngo.queries.GET_ADMINISTRATIVE_INVESTIGATION_DETAILS, **parameters)[0]
    
    ai_id = parameters.get("administrative_investigation", None)
    loan_request_id = administrative_investigation_details.pop("loan_request")
    
    # Get Child Tables for the Administrative Investigation
    administrative_investigation_details["ngo_activities"] = iam.db.get(administrative_investigation = ai_id, **parent_ngo.queries.GET_NGO_ACTIVITIES)
    administrative_investigation_details["microfinance_activities"] = iam.db.get(administrative_investigation = ai_id, **parent_ngo.queries.GET_MICROFINANCE_ACTIVITIES)
    # administrative_investigation_details["administrative_apparatus"] = iam.db.get(administrative_investigation = ai_id, **parent_ngo.queries.GET_ADMINISTRATIVE_APPARATUS)
    
    loan_request = get_loan_request_data(loan_request_id)
    
    return administrative_investigation_details, loan_request


def insert_administrative_investigation(id = None, **data):
    # Creates or Updates Administrative Investigation
    if id:
        administrative_investigation = frappe.get_doc("Administrative Investigation", id)
    else:
        administrative_investigation = frappe.new_doc("Administrative Investigation")
    
    add_to_table(administrative_investigation, "ngo_activities", data.pop("ngo_activities", None))
    add_to_table(administrative_investigation, "microfinance_activities", data.pop("microfinance_activities", None))
    # add_to_table(administrative_investigation, "administrative_apparatus", data.pop("administrative_apparatus", None))
    
    # Get attachment files : directorate's nid front & back
    attachments = prepare_attachments(id)
    for key in attachments:
        data[key] = attachments[key]
    
    # Update Status to in progress if it is an update
    if id:
        administrative_investigation.status = "In Progress"
    
    administrative_investigation.update(data)

    administrative_investigation.save()


def complete_administrative_investigation(id = None):
    administrative_investigation = frappe.get_doc("Administrative Investigation", id)

    administrative_investigation.status = "Completed"
    administrative_investigation.save()


def prepare_attachments(id):
    
    files = ["directorate_nid_front", "directorate_nid_back"]
    attachments = {}
    for file in files:
        attachment = upload_files("Administrative Investigation", id, [file], field=file, is_private = True)
        if attachment:
            attachments[file] = attachment[0].get("file_url")

    return attachments