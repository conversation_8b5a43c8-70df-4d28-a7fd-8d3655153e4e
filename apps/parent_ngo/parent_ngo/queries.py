# --------------------------------------------------------
######## Get Queries ########

# ----------------------------
######## Loan Request Queries ########

GET_LOAN_REQUEST_DETAILS = {
	"doctype": "Loan Request",
	"select_fields": {
		"Loan Request": {
			("ngo_name", "ngo_name"),
			("publication_number", "ngo_publication_number"),
			("address", "ngo_address"),
			("email", "ngo_email"),
			("publication_date", "ngo_publication_date"),
			("contact_full_name", "contact_person_full_name"),
			("contact_email", "contact_person_email"),
			("contact_mobile_no", "contact_person_mobile_no"),
		},
	},
	"conditions": {
		"loan_request": {"doctype": "Loan Request", "field": "name", "operator": "=="},
	},
	"expected_parameters": ["loan_request_id"],
	"required_parameters": ["loan_request_id"],
}


# ----------------------------
######## Financial Investigation Queries ########

GET_FINANCIAL_INVESTIGATIONS = {
	"doctype": "Financial Investigation",
	"select_fields": {
		"Financial Investigation": [
			("name", "financial_investigation_id"),
			("status", "status"),
		],
		"Loan Request": [
			("ngo_name", "ngo_name"),
			("contact_full_name", "contact_person"),
			("contact_email", "contact_email"),
			("contact_mobile_no", "contact_mobile_number"),
		]
	},
	"join_doctypes": {
		"Loan Request": ("name", "Financial Investigation", "loan_request")
	},
	"conditions": {
		"status": {"doctype": "Financial Investigation", "field": "status", "operator": "isin"},
		"excluded_status": {"doctype": "Financial Investigation", "field": "status", "operator": "notin"},
		"investigator": {"doctype": "Financial Investigation", "field": "investigator", "operator": "=="},
	},
	"expected_parameters": ["status"],
}


GET_FINANCIAL_INVESTIGATION_DETAILS = {
	"doctype": "Financial Investigation",
	"select_fields": {
		"Financial Investigation": [
			("name", "id"),
			("status", "status"),
			("loan_request", "loan_request"),
			("creation_date", "creation_date"),
			("previous_works_rating", "previous_works_rating"),
			("bod_meetings_record", "bod_meetings_record"),
			("ga_meetings_record", "ga_meetings_record"),
			("ms_record", "ms_record"),
			("visits_record", "visits_record"),
			("bwl_record", "bwl_record"),
            ("donations_record", "donations_record"),
			("cash_receipts", "cash_receipts"),
			("bank_deposits", "bank_deposits"),
			("exchange_notes", "exchange_notes"),
			("cheque_permits", "cheque_permits"),
			("bookkeeping", "bookkeeping"),
			("daily_journals", "daily_journals"),
			("ti_payments", "ti_payments"),
			("beneficiaries_portfolios", "beneficiaries_portfolios"),
			("financial_cycle_rating", "financial_cycle_rating"),
			("daily_journals_records", "daily_journals_records"),
			("daily_journals_records_precision", "daily_journals_records_precision"),
			("daily_journals_records_completion", "daily_journals_records_completion"),
			("customer_record_book", "customer_record_book"),
			("customer_record_book_precision", "customer_record_book_precision"),
			("customer_record_book_completion", "customer_record_book_completion"),
			("expenses_analytical_journal", "expenses_analytical_journal"),
			("expenses_analytical_journal_precision", "expenses_analytical_journal_precision"),
			("expenses_analytical_journal_completion", "expenses_analytical_journal_completion"),
			("ie_journal", "ie_journal"),
			("ie_journal_precision", "ie_journal_precision"),
			("ie_journal_completion", "ie_journal_completion"),
			("treasury_journal", "treasury_journal"),
			("treasury_journal_precision", "treasury_journal_precision"),
			("treasury_journal_completion", "treasury_journal_completion"),
            ("bank_book", "bank_book"),
            ("bank_book_precision", "bank_book_precision"),
			("bank_book_completion", "bank_book_completion"),
            ("assets_register", "assets_register"),
            ("assets_register_precision", "assets_register_precision"),
			("assets_register_completion", "assets_register_completion"),
			("financial_records_rating", "financial_records_rating"),
			("budget_account", "budget_account"),
			("re_account", "re_account"),
			("rp_account", "rp_account"),
			("cash_flow_account", "cash_flow_account"),
			("draft_budget_account", "draft_budget_account"),
			("fa_report", "fa_report"),
			("final_account_rating", "final_account_rating"),
			("comment", "comment"),
            ("previous_works_status", "previous_works_status"),
            ("previous_works_excellent_rating", "previous_works_excellent_rating"),
            ("previous_works_good_rating", "previous_works_good_rating"),
            ("previous_works_poor_rating", "previous_works_poor_rating"),
			("beneficiaries_portfolios_rating", "beneficiaries_portfolios_rating"),
			("daily_journals_records_rating", "daily_journals_records_rating"),
			("customer_record_book_rating", "customer_record_book_rating"),
			("budget_account_rating", "budget_account_rating"),
			("re_account_rating", "re_account_rating"),
			("rp_account_rating", "rp_account_rating"),
            ("fa_report_rating", "fa_report_rating"),
            ("cash_flow_status", "cash_flow_status"),
			("cash_flow_excellent_rating", "cash_flow_excellent_rating"),
			("cash_flow_very_good_rating", "cash_flow_very_good_rating"),
			("cash_flow_good_rating", "cash_flow_good_rating"),
            ("committee_decision", "committee_decision"),
            ("financial_document_cycle_rating", "financial_document_cycle_rating"),
            ("financial_investigation_rating", "financial_investigation_rating"),
            ("bod_meetings_record_rating", "bod_meetings_record_rating"),
            ("ga_meetings_record_rating", "ga_meetings_record_rating"),
            ("bwl_record_rating", "bwl_record_rating"),
            ("administrative_records_rating", "administrative_records_rating"),
		],
	},
	"conditions": {
		"financial_investigation": {"doctype": "Financial Investigation", "field": "name", "operator": "=="},
	},
	"expected_parameters": ["financial_investigation"],
	"required_parameters": ["financial_investigation"],
}


GET_PREVIOUS_WORK = {
	"doctype": "Previous Work",
	"select_fields": {
		"Previous Work": [
			("contract_date", "contract_date"),
			("loan_amount", "loan_amount"),
			("total_projects", "total_projects"),
		],
	},
	"conditions": {
		"financial_investigation": {"doctype": "Previous Work", "field": "parent", "operator": "=="},
	},
	"expected_parameters": ["financial_investigation"],
	"required_parameters": ["financial_investigation"],
}

# ----------------------------
######## Administrative Investigation Queries ########

GET_ADMINISTRATIVE_INVESTIGATIONS = {
	"doctype": "Administrative Investigation",
	"select_fields": {
		"Administrative Investigation": [
			("name", "administrative_investigation_id"),
			("status", "status"),
		],
		"Loan Request": [
			("ngo_name", "ngo_name"),
			("contact_full_name", "contact_person"),
			("contact_email", "contact_email"),
			("contact_mobile_no", "contact_mobile_number"),
		]
	},
	"join_doctypes": {
		"Loan Request": ("name", "Administrative Investigation", "loan_request")
	},
	"conditions": {
		"status": {"doctype": "Administrative Investigation", "field": "status", "operator": "isin"},
		"excluded_status": {"doctype": "Administrative Investigation", "field": "status", "operator": "notin"},
		"investigator": {"doctype": "Administrative Investigation", "field": "investigator", "operator": "=="},
	},
	"expected_parameters": ["status"],
}



GET_ADMINISTRATIVE_INVESTIGATION_DETAILS = {
	"doctype": "Administrative Investigation",
	"select_fields": {
		"Administrative Investigation": [
			("name", "id"),
			("status", "status"),
			("loan_request", "loan_request"),
			("creation_date", "creation_date"),
			("contact_full_name", "contact_full_name"),
			("contact_nid", "contact_nid"),
			("directorate_full_name", "directorate_full_name"),
			("directorate_nid_front", "directorate_nid_front"),
			("directorate_nid", "directorate_nid"),
			("directorate_nid_back", "directorate_nid_back"),
			("inspection_results", "inspection_results"),
			("inspection_date", "inspection_date"),
			("ownership_type", "ownership_type"),
			("ownership_date", "ownership_date"),
			("allocation_date", "allocation_date"),
			("rental_start_date", "rental_start_date"),
			("rental_end_date", "rental_end_date"),
            ("association_headquarters_address", "association_headquarters_address"),
			("comments", "comments"),
			("address_rating", "address_rating"),
			("beneficiaries_total", "beneficiaries_total"),
			("activities_rating", "activities_rating"),
			("microfinance_rating", "microfinance_rating"),
			("administrative_rating", "administrative_rating"),
			("ga_males", "ga_males"),
			("ga_females", "ga_females"),
			("ga_meetings", "ga_meetings"),
			("ga_last_meeting_date", "ga_last_meeting_date"),
			("bod_males", "bod_males"),
			("bod_females", "bod_females"),
			("bod_meetings", "bod_meetings"),
			("bod_last_meeting_date", "bod_last_meeting_date"),
			("bod_committees", "bod_committees"),
			("organizational_rating", "organizational_rating"),
			("comment", "comment"),
            ("microfinance_activities_status", "microfinance_activities_status"),
            ("microfinance_activities_excellent_rating", "microfinance_activities_excellent_rating"),
            ("microfinance_activities_good_rating", "microfinance_activities_good_rating"),
            ("general_assembly_memebers_rating", "general_assembly_memebers_rating"),
            ("general_assembly_females_members_rating", "general_assembly_females_members_rating"),
            ("general_assembly_meetings_rating", "general_assembly_meetings_rating"),
            ("directors_board_members_rating_5_9", "directors_board_members_rating_5_9"),
            ("directors_board_members_rating_11_15", "directors_board_members_rating_11_15"),
            ("directors_board_meetings_rating", "directors_board_meetings_rating"),
            ("directors_board_females_members_rating", "directors_board_females_members_rating"),
            ("organizational_chart_rating", "organizational_chart_rating"),
            ("manager_full_name", "manager_full_name"),
            ("manager_employment_date", "manager_employment_date"),
            ("manager_experience_years", "manager_experience_years"),
			("manager_insurance_status", "manager_insurance_status"),
            ("manager_rating", "manager_rating"),
            ("accountant_full_name", "accountant_full_name"),
            ("accountant_employment_date", "accountant_employment_date"),
            ("accountant_experience_years", "accountant_experience_years"),
			("accountant_insurance_status", "accountant_insurance_status"),
            ("accountant_rating", "accountant_rating"),
            ("officer_full_name", "officer_full_name"),
            ("officer_employment_date", "officer_employment_date"),
            ("officer_experience_years", "officer_experience_years"),
			("officer_insurance_status", "officer_insurance_status"),
            ("officer_rating", "officer_rating"),
            ("cashier_full_name", "cashier_full_name"),
            ("cashier_employment_date", "cashier_employment_date"),
            ("cashier_experience_years", "cashier_experience_years"),
			("cashier_insurance_status", "cashier_insurance_status"),
            ("cashier_rating", "cashier_rating"),
            ("investigator_full_name", "investigator_full_name"),
            ("investigator_employment_date", "investigator_employment_date"),
            ("investigator_experience_years", "investigator_experience_years"),
			("investigator_insurance_status", "investigator_insurance_status"),
            ("investigator_rating", "investigator_rating"),
		],
	},
	"conditions": {
		"administrative_investigation": {"doctype": "Administrative Investigation", "field": "name", "operator": "=="},
	},
	"expected_parameters": ["administrative_investigation"],
	"required_parameters": ["administrative_investigation"],
}


GET_NGO_ACTIVITIES = {
	"doctype": "NGO Activities",
	"select_fields": {
		"NGO Activities": {
			("activity", "activity"),
			("activity_start_date", "activity_start_date"),
			("financing_amount", "financing_amount"),
			("beneficiaries_total", "beneficiaries_total"),
			("donor", "donor"),
			("lending_status", "lending_status"),
			("end_date", "end_date"),
		}
	},
	"conditions": {
		"administrative_investigation": {"doctype": "NGO Activities", "field": "parent", "operator": "=="},
	},
	"expected_parameters": ["administrative_investigation"],
	"required_parameters": ["administrative_investigation"],
}


GET_MICROFINANCE_ACTIVITIES = {
	"doctype": "Microfinance Activities",
	"select_fields": {
		"Microfinance Activities": {
			("project_name", "project_name"),
			("project_start_date", "project_start_date"),
			("project_financing_amount", "project_financing_amount"),
			("beneficiaries_total", "beneficiaries_total"),
			("donor", "donor"),
			("total_financing_amount", "total_financing_amount"),
			("lending_type", "lending_type"),
			("repayment_status", "repayment_status"),
			("lending_status", "lending_status"),
			("end_date", "end_date"),
			("comment", "comment"),
		}
	},
	"conditions": {
		"administrative_investigation": {"doctype": "Microfinance Activities", "field": "parent", "operator": "=="},
	},
	"expected_parameters": ["administrative_investigation"],
	"required_parameters": ["administrative_investigation"],
}


# ----------------------------
######## Administrative Follow Up Queries ########

GET_ASSIGNED_AFUS = {
	"doctype": "Administrative Follow Up",
	"select_fields": {
		"Administrative Follow Up": [
			("name", "id"),
			("status", "status"),
			("creation_date", "created_on")
		],
		"Loan Request": [
			("ngo_name", "ngo_name"),
			("contact_full_name", "contact_person"),
			("contact_email", "contact_email"),
			("contact_mobile_no", "contact_mobile_number")
		]
	},
	"join_doctypes": {
		"Loan": ("name", "Administrative Follow Up", "loan"),
		"Loan Request": ("name", "Loan", "loan_request")
	},
	"conditions": {
		"status": {"doctype": "Administrative Follow Up", "field": "status", "operator": "isin"},
		"excluded_status": {"doctype": "Administrative Follow Up", "field": "status", "operator": "notin"},
		"investigator": {"doctype": "Administrative Follow Up", "field": "investigator", "operator": "=="},
	},
	"translatable_fields": ["status"],
	"expected_parameters": ["status"]
}

GET_AFU_DETAILS = {
	"doctype": "Administrative Follow Up",
	"select_fields": {
		"Administrative Follow Up": [
			("name", "id"),
			("status", "status"),
			("creation_date", "created_on"),
			("disbursement_aspects", "disbursement_aspects"),
			("customers_book", "customers_book"),
			("projects_sample_fu", "projects_sample_follow_up"),
			("projects_classification", "projects_classification"),
			("comment", "comment")
		],
		"Loan": [
			("loan_request", "loan_request")
		]
	},
	"join_doctypes": {
		"Loan": ("name", "Administrative Follow Up", "loan")
	},
	"conditions": {
		"follow_up_id": {"doctype": "Administrative Follow Up", "field": "name", "operator": "=="},
	},
	"translatable_fields": ["status", "disbursement_aspects", "customers_book", "projects_sample_follow_up", "projects_classification"],
	"expected_parameters": ["follow_up_id"],
	"required_parameters": ["follow_up_id"]
}


# ----------------------------
######## Financial Follow Up Queries ########

GET_ASSIGNED_FFUS = {
	"doctype": "Financial Follow Up",
	"select_fields": {
		"Financial Follow Up": [
			("name", "id"),
			("status", "status"),
			("creation_date", "created_on")
		],
		"Loan Request": [
			("ngo_name", "ngo_name"),
			("contact_full_name", "contact_person"),
			("contact_email", "contact_email"),
			("contact_mobile_no", "contact_mobile_number")
		]
	},
	"join_doctypes": {
		"Loan": ("name", "Financial Follow Up", "loan"),
		"Loan Request": ("name", "Loan", "loan_request")
	},
	"conditions": {
		"status": {"doctype": "Financial Follow Up", "field": "status", "operator": "isin"},
		"excluded_status": {"doctype": "Financial Follow Up", "field": "status", "operator": "notin"},
		"investigator": {"doctype": "Financial Follow Up", "field": "investigator", "operator": "=="},
	},
	"translatable_fields": ["status"],
	"expected_parameters": ["status"]
}

GET_FFU_DETAILS = {
	"doctype": "Financial Follow Up",
	"select_fields": {
		"Financial Follow Up": [
			("name", "id"),
			("status", "status"),
			("creation_date", "created_on"),
			("loan_cycle", "loan_cycle"),
			("bank_book_statement", "bank_book_statement"),
			("loans_customers_record", "loans_customers_record"),
			("daily_american_journals", "daily_american_journals"),
			("comment", "comment")
		],
		"Loan": [
			("loan_request", "loan_request")
		]
	},
	"join_doctypes": {
		"Loan": ("name", "Financial Follow Up", "loan")
	},
	"conditions": {
		"follow_up_id": {"doctype": "Financial Follow Up", "field": "name", "operator": "=="},
	},
	"translatable_fields": ["status", "loan_cycle", "bank_book_statement", "loans_customers_record", "daily_american_journals"],
	"expected_parameters": ["follow_up_id"],
	"required_parameters": ["follow_up_id"]
}


# --------------------------------------------------------
######## Create & Update Queries ########

# ----------------------------
######## Financial Investigation Queries ########


UPDATE_FINANCIAL_INVESTIGATION = {
	"expected_parameters": [
		"financial_investigation_id",
		"loan_request",
		"previous_works",
		"previous_works_rating",
		"bod_meetings_record",
		"ga_meetings_record",
		"ms_record",
		"visits_record",
		"bwl_record",
        "donations_record",
		"cash_receipts",
		"bank_deposits",
		"exchange_notes",
		"cheque_permits",
		"bookkeeping",
		"daily_journals",
		"ti_payments",
		"beneficiaries_portfolios",
		"financial_cycle_rating",
		"daily_journals_records",
		"daily_journals_records_precision",
		"daily_journals_records_completion",
		"customer_record_book",
		"customer_record_book_precision",
		"customer_record_book_completion",
		"expenses_analytical_journal",
		"expenses_analytical_journal_precision",
		"expenses_analytical_journal_completion",
		"ie_journal",
		"ie_journal_precision",
		"ie_journal_completion",
		"treasury_journal",
		"treasury_journal_precision",
		"treasury_journal_completion",
        "bank_book",
		"bank_book_precision",
		"bank_book_completion",
        "assets_register",
		"assets_register_precision",
		"assets_register_completion",
		"financial_records_rating",
		"budget_account",
		"re_account",
		"rp_account",
		"cash_flow_account",
		"draft_budget_account",
		"fa_report",
		"final_account_rating",
		"comment",
        "previous_works_status",
        "previous_works_excellent_rating",
        "previous_works_good_rating",
        "beneficiaries_portfolios_rating",
        "daily_journals_records_rating",
        "customer_record_book_rating",
        "budget_account_rating",
        "re_account_rating",
        "rp_account_rating",
        "fa_report_rating",
        "cash_flow_status",
        "cash_flow_excellent_rating",
        "cash_flow_very_good_rating",
        "cash_flow_good_rating",
        "bod_meetings_record_rating",
        "ga_meetings_record_rating",
        "bwl_record_rating",
        "committee_decision",
	],
	"required_parameters": [
		"financial_investigation_id",
	],
	"expected_parameters_v1": [
		"fi_id",
		"loan_request",
		"previous_works",
		"previous_works_rating",
		"bod_meetings_record",
		"ga_meetings_record",
		"ms_record",
		"visits_record",
		"bwl_record",
        "donations_record",
		"cash_receipts",
		"bank_deposits",
		"exchange_notes",
		"cheque_permits",
		"bookkeeping",
		"daily_journals",
		"ti_payments",
		"beneficiaries_portfolios",
		"financial_cycle_rating",
		"daily_journals_records",
		"daily_journals_records_precision",
		"daily_journals_records_completion",
		"customer_record_book",
		"customer_record_book_precision",
		"customer_record_book_completion",
		"expenses_analytical_journal",
		"expenses_analytical_journal_precision",
		"expenses_analytical_journal_completion",
		"ie_journal",
		"ie_journal_precision",
		"ie_journal_completion",
		"treasury_journal",
		"treasury_journal_precision",
		"treasury_journal_completion",
        "bank_book",
		"bank_book_precision",
		"bank_book_completion",
		"assets_register",
		"assets_register_precision",
		"assets_register_completion",
		"financial_records_rating",
		"budget_account",
		"re_account",
		"rp_account",
		"cash_flow_account",
		"draft_budget_account",
		"fa_report",
		"final_account_rating",
		"comment",
        "previous_works_status",
        "previous_works_excellent_rating",
        "previous_works_good_rating",
        "beneficiaries_portfolios_rating",
        "daily_journals_records_rating",
        "customer_record_book_rating",
        "budget_account_rating",
        "re_account_rating",
        "rp_account_rating",
        "fa_report_rating",
        "cash_flow_status",
        "cash_flow_excellent_rating",
        "cash_flow_very_good_rating",
        "cash_flow_good_rating",
        "bod_meetings_record_rating",
        "ga_meetings_record_rating",
        "bwl_record_rating",
        "committee_decision",
	],
	"required_parameters_v1": [
		"fi_id",
	]
}


# ----------------------------
######## Administrative Investigation Queries ########


UPDATE_ADMINISTRATIVE_INVESTIGATION = {
	"expected_parameters": [
		"administrative_investigation_id",
		"loan_request",
		"contact_full_name",
		"contact_nid",
		"directorate_full_name",
		"directorate_nid",
		"inspection_results",
		"inspection_date",
		"ownership_type",
		"ownership_date",
		"allocation_date",
		"rental_start_date",
		"rental_end_date",
        "association_headquarters_address",
		"comments",
		"address_rating",
		"activities_rating",
		"microfinance_rating",
		"administrative_rating",
		"ga_males",
		"ga_females",
		"ga_meetings",
		"ga_last_meeting_date",
		"bod_males",
		"bod_females",
		"bod_meetings",
		"bod_last_meeting_date",
		"bod_committees",
		"organizational_rating",
		"comment",
		"ngo_activities",
		"microfinance_activities",
		"microfinance_activities_status",
		"microfinance_activities_excellent_rating",
		"microfinance_activities_good_rating",
        "general_assembly_memebers_rating",
        "general_assembly_females_members_rating",
        "general_assembly_meetings_rating",
        "directors_board_members_rating_5_9",
        "directors_board_members_rating_11_15",
        "directors_board_meetings_rating",
        "directors_board_females_members_rating",
        "manager_full_name",
        "manager_employment_date",
        "manager_experience_years", 
		"manager_insurance_status",
        "manager_rating",
        "accountant_full_name",
        "accountant_employment_date",
        "accountant_experience_years",
        "accountant_insurance_status",
        "accountant_rating",
        "officer_full_name",
        "officer_employment_date",
        "officer_experience_years",
        "officer_insurance_status",
        "officer_rating",
        "cashier_full_name",
        "cashier_employment_date",
        "cashier_experience_years",
        "cashier_insurance_status",
        "cashier_rating",
        "investigator_full_name",
        "investigator_employment_date",
        "investigator_experience_years",
        "investigator_insurance_status",
        "investigator_rating",
        "ngo_activities_rating",
	],
	"required_parameters": [
		"administrative_investigation_id",
	],
	"expected_parameters_v1": [
		"ai_id",
		"loan_request",
		"contact_full_name",
		"contact_nid",
		"directorate_full_name",
		"directorate_nid",
		"inspection_results",
		"inspection_date",
		"ownership_type",
		"ownership_date",
		"allocation_date",
		"rental_start_date",
		"rental_end_date",
        "association_headquarters_address",
		"comments",
		"address_rating",
		"activities_rating",
		"microfinance_rating",
		"administrative_rating",
		"ga_males",
		"ga_females",
		"ga_meetings",
		"ga_last_meeting_date",
		"bod_males",
		"bod_females",
		"bod_meetings",
		"bod_last_meeting_date",
		"bod_committees",
		"organizational_rating",
		"comment",
		"ngo_activities",
		"microfinance_activities",
		"microfinance_activities_status",
		"microfinance_activities_excellent_rating",
		"microfinance_activities_good_rating",
        "general_assembly_memebers_rating",
        "general_assembly_females_members_rating",
        "general_assembly_meetings_rating",
        "directors_board_members_rating_5_9",
        "directors_board_members_rating_11_15",
        "directors_board_meetings_rating",
        "directors_board_females_members_rating",
        "manager_full_name",
        "manager_employment_date",
        "manager_experience_years", 
		"manager_insurance_status",
        "manager_rating",
        "accountant_full_name",
        "accountant_employment_date",
        "accountant_experience_years",
        "accountant_insurance_status",
        "accountant_rating",
        "officer_full_name",
        "officer_employment_date",
        "officer_experience_years",
        "officer_insurance_status",
        "officer_rating",
        "cashier_full_name",
        "cashier_employment_date",
        "cashier_experience_years",
        "cashier_insurance_status",
        "cashier_rating",
        "investigator_full_name",
        "investigator_employment_date",
        "investigator_experience_years",
        "investigator_insurance_status",
        "investigator_rating",
        "ngo_activities_rating",
	],
	"required_parameters_v1": [
		"ai_id",
	]
}

# ----------------------------
######## Administrative Follow Up Queries ########


UPDATE_AFU = {
	"expected_parameters": [
		"follow_up_id",
		"disbursement_aspects",
		"customers_book",
		"projects_sample_fu",
		"projects_classification",
		"comment"
	],
	"required_parameters": [
		"follow_up_id",
	],
	"expected_parameters_v1": [
		"afu_id",
		"disbursement_aspects",
		"customers_book",
		"projects_sample_fu",
		"projects_classification",
		"comment"
	],
	"required_parameters_v1": [
		"afu_id",
	]
}

# ----------------------------
######## Financial Follow Up Queries ########

UPDATE_FFU = {
	"expected_parameters": [
		"follow_up_id",
		"loan_cycle",
		"bank_book_statement",
		"loans_customers_record",
		"daily_american_journals",
		"comment"
	],
	"required_parameters": [
		"follow_up_id",
	],
	"expected_parameters_v1": [
		"ffu_id",
		"loan_cycle",
		"bank_book_statement",
		"loans_customers_record",
		"daily_american_journals",
		"comment"
	],
	"required_parameters_v1": [
		"ffu_id",
	]
}
