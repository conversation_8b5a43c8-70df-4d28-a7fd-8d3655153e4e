# Copyright (c) 2024, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe, iam, parent_ngo
from frappe import _, share
from frappe.utils import nowdate, date_diff
from frappe.model.document import Document


class AdministrativeInvestigation(Document):
	def before_validate(self):
		self.set_docs()
		self.set_creation_date()
		self.clear_ownership_dates()
		self.clear_microfinance_activities_ratings()
		self.clear_directors_board_ratings()
		self.set_activities_beneficiaries_total()
		self.apply_organizational_chart_rating()
		self.apply_administrative_apparatus_rating()

	def validate(self):
		self.validate_mandatory_fields()
		self.validate_nids()
		self.validate_active_investigations()
		self.validate_microfinance_activities_ratings()
		self.validate_directors_board_ratings()
		self.validate_ngo_activities_ratings()

	def on_trash(self):
		self.validate_delete_status()
		self.delete_child_investigation()
		self.unshare_with_self()

	def on_update(self):
		self.create_child_investigation()
		self.share_with_self()

	def set_docs(self):
		# Sets Loan Request document for easy access
		self.loan_request_doc = frappe.get_doc("Loan Request", self.loan_request)

	def set_creation_date(self):
		# Sets creation date to today if not set
		if not self.creation_date:
			self.creation_date = nowdate()

	def clear_ownership_dates(self):
		# Clears ownership dates fields depending on the selected ownership type
		if self.ownership_type != "Owned":
			self.ownership_date = None
		if self.ownership_type != "Allocated":
			self.allocation_date = None
		if self.ownership_type != "Rental":
			self.rental_start_date = self.rental_end_date = None

	def clear_microfinance_activities_ratings(self):
		if self.microfinance_activities_status and self.microfinance_activities_status == "Excellent":
			self.microfinance_activities_good_rating = None
		elif self.microfinance_activities_status and self.microfinance_activities_status == "Good":
			self.microfinance_activities_excellent_rating = None

	def clear_directors_board_ratings(self):
		bod_males = self.bod_males or 0
		bod_females = self.bod_females or 0
		total_bod = bod_males + bod_females

		if 5 <= total_bod <= 9:
			self.directors_board_members_rating_11_15 = None
		elif total_bod >= 10:
			self.directors_board_members_rating_5_9 = None

	def set_activities_beneficiaries_total(self):
		# Sets the total number of beneficiaries from NGO Activities
		beneficiaries_total = 0
		for activity in self.ngo_activities:
			beneficiaries_total += activity.beneficiaries_total
		self.beneficiaries_total = beneficiaries_total

	def apply_organizational_chart_rating(self):
		# Calculates the totoal organizational chart rating
		total_rating = 0
		fields = ["general_assembly_memebers_rating", "general_assembly_females_members_rating", "general_assembly_meetings_rating", "directors_board_meetings_rating", "directors_board_females_members_rating"]
		for field in fields:
			if self.get(field):
				total_rating += int(self.get(field))
		bod_males = self.bod_males or 0
		bod_females = self.bod_females or 0
		total_bod = bod_males + bod_females

		if 5 <= total_bod <= 9:
			total_rating += int(self.directors_board_members_rating_5_9 or 0)
		elif total_bod >= 10:
			total_rating += int(self.directors_board_members_rating_11_15 or 0)

		self.organizational_chart_rating = total_rating

	def apply_administrative_apparatus_rating(self):
		# Calculates the totoal administrative apparatus rating
		total_rating = 0
		fields = ["manager_rating", "accountant_rating", "officer_rating", "cashier_rating", "investigator_rating"]
		for field in fields:
			if self.get(field):
				total_rating += int(self.get(field))
		self.administrative_apparatus_rating = total_rating

	def validate_mandatory_fields(self):
		# Validates the mandatory fieds on completion
		if self.status != "Completed":
			return

		fields = [
			"contact_full_name", "contact_nid", "directorate_full_name", 
			"directorate_nid_front", "directorate_nid", "directorate_nid_back", 
			"inspection_results", "inspection_date", "ownership_type", "comments", 
			"ngo_activities", "beneficiaries_total", "ga_males", "ga_females", 
			"ga_meetings", "ga_last_meeting_date", "bod_males", "bod_females", 
			"bod_meetings", "bod_last_meeting_date", "bod_committees", "committee_decision", "comment", 
			"microfinance_activities_status", "general_assembly_memebers_rating", 
			"general_assembly_females_members_rating", "general_assembly_meetings_rating", 
			"directors_board_meetings_rating", "directors_board_females_members_rating",
			"manager_full_name", "manager_employment_date", "manager_experience_years", 
			"manager_insurance_status", "manager_rating", 
			"accountant_full_name", "accountant_employment_date", "accountant_experience_years", 
			"accountant_insurance_status", "accountant_rating",
			"officer_full_name", "officer_employment_date", "officer_experience_years", 
			"officer_insurance_status", "officer_rating",
			"cashier_full_name", "cashier_employment_date", "cashier_experience_years", 
			"cashier_insurance_status", "cashier_rating",
			"investigator_full_name", "investigator_employment_date", "investigator_experience_years", 
			"investigator_insurance_status", "investigator_rating",
		]

		for field in fields:
			if not self.get(field):
				exception = iam.MandatoryFieldError(targets=field)
				iam.throw(_(parent_ngo.ERRORS.get("missing_mandatory_field")).format(_(self.meta.get_label(field))), exception=exception)

		self.validate_ownership_dates()
		self.validate_lending_end_dates()
		# Conditional validation for association_headquarters_address
		if self.ownership_type and not self.association_headquarters_address:
			exception = iam.MandatoryFieldError(targets="association_headquarters_address")
			iam.throw(_(parent_ngo.ERRORS.get("missing_mandatory_field")).format(_(self.meta.get_label("association_headquarters_address"))),exception=exception)

	def validate_ownership_dates(self):
		# Validates if the ownership date is set depending on the selected ownership type
		if self.ownership_type == "Owned":
			if not self.ownership_date:
				exception = iam.MandatoryFieldError(targets="ownership_date")
				iam.throw(_(parent_ngo.ERRORS.get("mandatory_ownership_date")), exception=exception)
		elif self.ownership_type == "Allocated":
			if not self.allocation_date:
				exception = iam.MandatoryFieldError(targets="allocation_date")
				iam.throw(_(parent_ngo.ERRORS.get("mandatory_allocation_date")), exception=exception)
		elif self.ownership_type == "Rental":
			if not self.rental_start_date:
				exception = iam.MandatoryFieldError(targets="rental_start_date")
				iam.throw(_(parent_ngo.ERRORS.get("mandatory_rental_dates")), exception=exception)
			elif not self.rental_end_date:
				exception = iam.MandatoryFieldError(targets="rental_end_date")
				iam.throw(_(parent_ngo.ERRORS.get("mandatory_rental_dates")), exception=exception)
			if date_diff(self.rental_end_date, self.rental_start_date) <= 0:
				exception = parent_ngo.InvalidRentalDatesError()
				iam.throw(_(parent_ngo.ERRORS.get("invalid_rental_dates")), exception=exception)

	def validate_lending_end_dates(self):
		# Validates if the end date is set when the lending status is active
		for activity in self.ngo_activities:
			if activity.lending_status == "Active" and not activity.end_date:
				exception = iam.MandatoryTableFieldError(targets={"target": "end_date", "target_table": "ngo_activities"}, row_idx=activity.idx)
				iam.throw(_(parent_ngo.ERRORS.get("mandatory_lending_end_date")).format(activity.idx, _("NGO Activities")), exception=exception)
		for mf_activity in self.microfinance_activities:
			if mf_activity.lending_status == "Active" and not mf_activity.end_date:
				exception = iam.MandatoryTableFieldError(targets={"target": "end_date", "target_table": "microfinance_activities"}, row_idx=mf_activity.idx)
				iam.throw(_(parent_ngo.ERRORS.get("mandatory_lending_end_date")).format(mf_activity.idx, _("Microfinance Activities")), exception=exception)
			if mf_activity.lending_status == "Active" and mf_activity.end_date < mf_activity.project_start_date:
				exception = parent_ngo.InvalidLendingEndDateError(targets={"target":"end_date", "target_table":"microfinance_activities"}, row_idx=mf_activity.idx)
				iam.throw(_(parent_ngo.ERRORS.get("invalid_lending_end_date")).format(mf_activity.idx, _("Microfinance Activities")), exception=exception)

	def validate_nids(self):
		# Validates if the provided National IDs are valid
		if self.status != "Completed":
			return
		fields = ["contact_nid", "directorate_nid"]
		for field in fields:
			if len(self.get(field)) != 14 or not self.get(field).isdigit():
				exception = iam.InvalidNIDFieldError(targets=field)
				iam.throw(_(parent_ngo.ERRORS.get(f"invalid_{field}")), exception=exception)

	def validate_active_investigations(self):
		# Validates if there are no current Opened or In Progress investigations for the same loan request
		has_active_investigations = frappe.db.exists("Administrative Investigation", {
			"name": ["!=", self.name],
			"status": ["in", ["Opened", "In Progress"]],
			"loan_request": self.loan_request,
		})
		if has_active_investigations:
			exception = parent_ngo.InvestigationInProgressError(targets={"investigation_type": "administrative"})
			iam.throw(_(parent_ngo.ERRORS.get("duplicate_administrative_investigation")), exception=exception)

	def validate_microfinance_activities_ratings(self):
		if self.microfinance_activities_status == "Excellent" and not self.microfinance_activities_excellent_rating:
			exception = iam.MandatoryFieldError(targets="microfinance_activities_excellent_rating")
			iam.throw(_(parent_ngo.ERRORS.get("missing_mandatory_field")).format(_("Microfinance Activities Excellent Rating")), exception=exception)
		elif self.microfinance_activities_status == "Good" and not self.microfinance_activities_good_rating:
			exception = iam.MandatoryFieldError(targets="microfinance_activities_good_rating")
			iam.throw(_(parent_ngo.ERRORS.get("missing_mandatory_field")).format(_("Microfinance Activities Good Rating")), exception=exception)

	def validate_directors_board_ratings(self):
		bod_males = self.bod_males or 0
		bod_females = self.bod_females or 0
		total_bod = bod_males + bod_females

		if 5 <= total_bod <= 9:
			if not self.directors_board_members_rating_5_9:
				exception = iam.MandatoryFieldError(targets="directors_board_members_rating_5_9")
				iam.throw(
					_(parent_ngo.ERRORS.get("missing_mandatory_field")).format(_("Directors Board Members Rating 5-9")),
					exception=exception
				)
		elif total_bod >= 10:
			if not self.directors_board_members_rating_11_15:
				exception = iam.MandatoryFieldError(targets="directors_board_members_rating_11_15")
				iam.throw(
					_(parent_ngo.ERRORS.get("missing_mandatory_field")).format(_("Directors Board Members Rating 11-15")),
					exception=exception
				)

	def validate_ngo_activities_ratings(self):
		# Validates if the NGO Activities Rating is set based on the Lending Status
		if not self.ngo_activities:
			return

		statuses = [a.lending_status for a in self.get("ngo_activities") if a.lending_status]
		if not statuses:
			return

		only_ended = all(status == "Ended" for status in statuses)

		valid_range = range(0, 4) if only_ended else range(4, 11)

		if self.ngo_activities_rating:
			rating = int(self.ngo_activities_rating)
			if rating not in valid_range:
				exception = parent_ngo.InvalidNgoActivitiesRatingError(min_rating=min(valid_range), max_rating=max(valid_range))
				iam.throw(
					_(parent_ngo.ERRORS.get("invalid_ngo_activities_rating")).format(min(valid_range), max(valid_range)),
					exception=exception
				)

	def create_child_investigation(self):
		# Creates or updates administrative investigation document into the child table of the loan request
		if self.child_doc:
			child_investigation_doc = frappe.get_doc("Administrative Investigation Details", self.child_doc)
			child_investigation_doc.status = self.status
			child_investigation_doc.comment = self.comment
			child_investigation_doc.save()
		else:
			child_investigation_doc = self.loan_request_doc.append("administrative_investigations", {
				"status": self.status,
				"investigation": self.name,
				"investigator": self.investigator
			})
			self.loan_request_doc.save()
			self.child_doc = child_investigation_doc.name
			self.save()

	def validate_delete_status(self):
		# Validates if the administrative investigation is not Completed
		if self.status == "Completed":
			exception = parent_ngo.CompletedInvestigationDeletionError(targets={"investigation_type": "administrative"})
			iam.throw(_(parent_ngo.ERRORS.get("invalid_investigation_delete_status")), exception=exception)

	def delete_child_investigation(self):
		# Deletes the administrative investigation document from the child table of the loan request
		frappe.delete_doc("Administrative Investigation Details", self.child_doc, ignore_permissions=True)

	def share_with_self(self):
		# Share the Administrative Investigation & Loan Request Document with the investigator associated with it
		if self.status == "Cancelled":
			self.unshare_with_self()
		else:
			write = 0 if self.status == "Completed" else 1
			share.add_docshare(self.doctype, self.name, self.investigator, write=write, flags={"ignore_share_permission": True})
			share.add_docshare("Loan Request", self.loan_request, self.investigator, write=write, flags={"ignore_share_permission": True})

	def unshare_with_self(self):
		# Remove share assignment from Loan Request Document on deletion of Administrative Investigation
		share.remove("Loan Request", self.loan_request, self.investigator, flags={"ignore_permissions": True, "ignore_share_permission": True})
