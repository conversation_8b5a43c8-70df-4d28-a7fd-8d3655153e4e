// Copyright (c) 2024, BrainWise and contributors
// For license information, please see license.txt

frappe.ui.form.on("Administrative Investigation", {
    setup(frm) {
        frm.set_query('investigator', () => {
            return {
                query: "parent_ngo.utils.user.user_query",
                filters: [["Has Role", "role", "=", "NGO Investigator"]]
            }
        })
    },
    refresh: function(frm) {
        update_rating_options(frm);
    }
});

frappe.ui.form.on('NGO Activities', {
    beneficiaries_total: function (frm, cdt, cdn) {
        calculateBeneficiariesTotal(frm)
    },
    ngo_activities_on_form_rendered: function(frm, cdt, cdn) {
        update_rating_options(frm);
    },
    ngo_activities_add: function (frm, cdt, cdn) {
        update_rating_options(frm);
    },
    ngo_activities_remove: function (frm, cdt, cdn) {
        calculateBeneficiariesTotal(frm)
        update_rating_options(frm);
    }
});

function calculateBeneficiariesTotal(frm) {
    let beneficiariesTotal = 0
    frm.doc.ngo_activities.forEach(activity => {
        beneficiariesTotal += activity.beneficiaries_total
    })
    frm.set_value("beneficiaries_total", beneficiariesTotal)
}

function update_rating_options(frm) {
    const activities = frm.doc.ngo_activities || [];
    const statuses = activities.map(row => row.lending_status).filter(Boolean);

    if (statuses.length === 0) {
        frm.set_df_property('ngo_activities_rating', 'options', ['Please select']);
        frm.set_value('ngo_activities_rating', '');
        return;
    }

    const only_ended = statuses.every(status => status === 'Ended');

    // If all Ended: 0-3, else (Active or Mixed): 4-10
    const options = only_ended
        ? ['0', '1', '2', '3']
        : ['4', '5', '6', '7', '8', '9', '10'];

    frm.set_df_property('ngo_activities_rating', 'options', options);
}
