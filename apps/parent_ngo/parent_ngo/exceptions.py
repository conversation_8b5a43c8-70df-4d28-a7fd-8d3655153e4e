import iam


class InvestigatorForbiddenError(iam.ForbiddenError):
	specific_code = "403-2-1"
	message = "You do not have the necessary permissions to access this resource or perform this action. Please ensure that you are the assigned investigator."


# ----------------------------------------


class InvestigationNotFoundError(iam.NotFoundError):
	has_targets = True
	default_targets = ["administrative", "financial"]
	specific_code = "404-2-1"
	message = "The {investigation_type} investigation '{investigation_id}' could not be found in the system."

class FollowUpNotFoundError(iam.NotFoundError):
	has_targets = True
	default_targets = ["administrative", "financial"]
	specific_code = "404-2-2"
	message = "The {follow_up_type} follow-up '{follow_up_id}' could not be found in the system."


# ----------------------------------------


class DuplicateBeneficiaryError(iam.ConflictError):
	has_targets = True
	specific_code = "409-2-1"
	message = "The beneficiary in the '{target_table}' table at row #{row_idx} has already been added. Duplicate entries are not allowed."

class BeneficiaryInProgressError(iam.ConflictError):
	has_targets = True
	specific_code = "409-2-2"
	message = "The beneficiary in the '{target_table}' table at row #{row_idx} already has active loans or loan requests in the selected loan program."

class InvestigationInProgressError(iam.ConflictError):
	has_targets = True
	default_targets = ["administrative", "financial"]
	specific_code = "409-2-3"
	message = "An existing {investigation_type} investigation is already in progress for this loan request."

class CompletedInvestigationError(iam.ConflictError):
	has_targets = True
	default_targets = ["administrative", "financial"]
	specific_code = "409-2-4"
	message = "The {investigation_type} investigation cannot be retrieved or updated when its status is completed."

class CompletedInvestigationDeletionError(iam.ConflictError):
	has_targets = True
	default_targets = ["administrative", "financial"]
	specific_code = "409-2-5"
	message = "The {investigation_type} investigation cannot be deleted when its status is completed."

class FollowUpInProgressError(iam.ConflictError):
	has_targets = True
	default_targets = ["administrative", "financial"]
	specific_code = "409-2-6"
	message = "An existing {follow_up_type} follow-up is already in progress for this loan."

class CompletedFollowUpError(iam.ConflictError):
	has_targets = True
	default_targets = ["administrative", "financial"]
	specific_code = "409-2-7"
	message = "The {follow_up_type} follow-up cannot be retrieved or updated when its status is completed."

class CompletedFollowUpDeletionError(iam.ConflictError):
	has_targets = True
	default_targets = ["administrative", "financial"]
	specific_code = "409-2-8"
	message = "The {follow_up_type} follow-up cannot be deleted when its status is completed."


# ----------------------------------------


class InvalidLoanProgramFundPoolError(iam.UnprocessableEntityError):
	specific_code = "422-2-1"
	message = "The fund pool must be greater than or equal to the sanctioned funds."

class InvalidLoanProgramAmountCriteriaError(iam.UnprocessableEntityError):
	specific_code = "422-2-2"
	message = "The maximum loan amount must be greater than or equal to the minimum loan amount."

class LoanProgramAmountCriteriaMismatchError(iam.UnprocessableEntityError):
	specific_code = "422-2-3"
	message = "The requested loan amount does not meet the criteria for the selected loan program."

class InsufficientLoanProgramFundsError(iam.UnprocessableEntityError):
	specific_code = "422-2-4"
	message = "There are not enough funds in the loan program's pool to cover the requested loan amount."

class TotalPaidExceedsDisbursedAmountError(iam.UnprocessableEntityError):
	specific_code = "422-2-5"
	message = "The total amount paid against this loan cannot exceed the total disbursed amount."

class LoanTotalsInconsistencyError(iam.UnprocessableEntityError):
	specific_code = "422-2-6"
	message = "The loan totals do not match. Please verify that they align with the repayment schedule."

class LoanRemainingPaymentsInconsistencyError(iam.UnprocessableEntityError):
	specific_code = "422-2-7"
	message = "The loan remaining payments do not match. Please verify that they align with the repayment schedule."

class InvalidDisbursementAmountError(iam.UnprocessableEntityError):
	specific_code = "422-2-8"
	message = "The disbursement amount must be greater than zero."

class DisbursementAmountExceededLoanBalanceError(iam.UnprocessableEntityError):
	specific_code = "422-2-9"
	message = "The disbursement amount cannot exceed the remaining balance of the loan."

class LoanRepaymentAmountsInconsistencyError(iam.UnprocessableEntityError):
	specific_code = "422-2-10"
	message = "The payment amounts do not match. Please verify that they align with the repayment schedule."

class IncompleteInvestigationError(iam.UnprocessableEntityError):
	specific_code = "422-2-11"
	message = "{investigation_type} investigations must be finalized with at least one marked as Completed, and none Open or In Progress."

class InvalidRentalDatesError(iam.UnprocessableEntityError):
	specific_code = "422-2-12"
	message = "The rental start date must occur before the rental end date. Please adjust the dates and try again."

class TermsAndConditionsNotAcceptedError(iam.UnprocessableEntityError):
	specific_code = "422-2-13"
	message = "You must accept the Terms and Conditions before proceeding."

class InvalidLendingEndDateError(iam.UnprocessableEntityError):
	has_targets = True
	specific_code = "422-2-14"
	message = "Lending End Date in the '{target_table}' table at row #{row_idx} must be a date after Project Start Date."

class MissingRequiredAttachmentsError(iam.UnprocessableEntityError):
	has_targets = True
	specific_code = "422-2-15"
	message = "Required attachments are missing: '{missing_attachments}'."

class IncompleteIScoreDataError(iam.UnprocessableEntityError):
    specific_code = "422-2-16"
    message = "Please fill in all required fields for i-SCORE data."

class InvalidNgoActivitiesRatingError(iam.UnprocessableEntityError):
	specific_code = "422-2-16"
	message = "NGO Activities Rating must be between {min_rating} and {max_rating} based on the Lending Status."