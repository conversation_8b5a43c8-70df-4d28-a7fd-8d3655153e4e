{"actions": [], "allow_rename": 1, "autoname": "naming_series:naming_series", "creation": "2024-03-06 12:39:56.227423", "doctype": "DocType", "engine": "InnoDB", "field_order": ["status", "loan_status", "loan", "column_break_wutr", "creation_date", "naming_series", "ngo_details_tab", "ngo", "clear_ngo", "fetch_ngo_attachments", "column_break_ipce", "details_section", "ngo_name", "publication_number", "license_number", "address", "column_break_jymt", "email", "publication_date", "license_date", "contact_details_section", "contact_full_name", "contact_email", "column_break_bzok", "contact_nid", "contact_mobile_no", "section_break_cjxn", "contact_nid_front", "contact_nid_front_preview", "column_break_wvqo", "contact_nid_back", "contact_nid_back_preview", "treasurer_details_section", "treasurer_full_name", "column_break_gtwj", "treasurer_nid", "section_break_ngjt", "treasurer_nid_front", "image_fibg", "column_break_jnhl", "treasurer_nid_back", "image_nfxf", "section_break_yjsu", "treasurer_iscore_status", "treasurer_iscore_result", "treasurer_iscore_loans_number", "treasurer_iscore_total_balance", "column_break_ybul", "treasurer_iscore_doc", "treasurer_iscore_report", "treasurer_iscore_report_id", "ceo_details_section", "ceo_full_name", "column_break_svbm", "ceo_nid", "section_break_rrpi", "ceo_nid_front", "ceo_nid_front_preview", "column_break_rabw", "ceo_nid_back", "ceo_nid_back_preview", "section_break_jqcc", "ceo_iscore_status", "ceo_iscore_result", "ceo_iscore_loans_number", "ceo_iscore_total_balance", "column_break_mvpi", "ceo_iscore_report", "ceo_iscore_doc", "ceo_iscore_report_id", "board_members_details_section", "board_members", "tab_2_tab", "beneficiaries", "section_break_plgp", "column_break_kkma", "column_break_qzjg", "total_loan_amount", "loan_details_tab", "loan_details_section", "loan_type", "column_break_edpk", "requested_loan_amount", "loan_program_details_section", "loan_program", "column_break_cylf", "loan_program_name", "section_break_ziye", "interest_rate", "column_break_owep", "installment_period", "attachments_tab", "lr_document", "fra_permit", "bod_statement", "oc_statement", "terms_tab", "terms", "section_break_oawu", "column_break_wqtv", "column_break_aqkm", "accept_terms", "review_tab", "field_investigation_section", "financial_investigations", "administrative_investigations", "extra_documents_section", "extra_documents", "section_break_xeem", "hc_report"], "fields": [{"fieldname": "ngo_details_tab", "fieldtype": "Tab Break", "label": "NGO Details"}, {"fieldname": "ngo", "fieldtype": "Link", "in_standard_filter": 1, "label": "NGO", "options": "Customer NGO", "read_only_depends_on": "ngo", "reqd": 1}, {"depends_on": "eval: doc.status == \"Draft\" && doc.ngo", "fieldname": "clear_ngo", "fieldtype": "<PERSON><PERSON>", "label": "Clear NGO", "permlevel": 1}, {"fieldname": "column_break_ipce", "fieldtype": "Column Break"}, {"fieldname": "naming_series", "fieldtype": "Select", "hidden": 1, "label": "Naming Series", "no_copy": 1, "options": "LR-.#", "read_only": 1, "set_only_once": 1}, {"fieldname": "details_section", "fieldtype": "Section Break", "label": "Details"}, {"fieldname": "ngo_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Name", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_jymt", "fieldtype": "Column Break"}, {"fieldname": "tab_2_tab", "fieldtype": "Tab Break", "label": "Beneficiaries Details"}, {"fieldname": "publication_number", "fieldtype": "Data", "in_standard_filter": 1, "label": "Publication Number", "read_only": 1, "reqd": 1}, {"fieldname": "license_number", "fieldtype": "Data", "in_standard_filter": 1, "label": "License Number", "read_only": 1, "reqd": 1}, {"fieldname": "address", "fieldtype": "Data", "label": "Address", "read_only": 1, "reqd": 1}, {"fieldname": "email", "fieldtype": "Data", "in_standard_filter": 1, "label": "Email", "read_only": 1, "reqd": 1}, {"fieldname": "publication_date", "fieldtype": "Date", "label": "Publication Date", "read_only": 1, "reqd": 1}, {"fieldname": "license_date", "fieldtype": "Date", "label": "License Date", "read_only": 1, "reqd": 1}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Draft\nPending CN Review\nPending LO Review\nPending LS Review\nPending Field Investigation\nPending i-Score\nPending LM Review\nPending Extra Information\nPending HC Review\nApproved\nPostponed\nRejected", "read_only": 1, "reqd": 1}, {"fieldname": "contact_details_section", "fieldtype": "Section Break", "label": "Contact Details"}, {"fieldname": "contact_full_name", "fieldtype": "Data", "label": "Full Name", "read_only": 1, "reqd": 1}, {"fieldname": "contact_email", "fieldtype": "Data", "label": "Email", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_bzok", "fieldtype": "Column Break"}, {"fieldname": "contact_nid", "fieldtype": "Data", "label": "National ID", "read_only": 1, "reqd": 1}, {"fieldname": "contact_mobile_no", "fieldtype": "Data", "label": "Mobile Number", "read_only": 1, "reqd": 1}, {"fieldname": "section_break_cjxn", "fieldtype": "Section Break"}, {"depends_on": "eval: !doc.__islocal && doc.ngo", "fieldname": "contact_nid_front", "fieldtype": "Attach Image", "label": "National ID Front", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"depends_on": "contact_nid_front", "fieldname": "contact_nid_front_preview", "fieldtype": "Image", "options": "contact_nid_front"}, {"fieldname": "column_break_wvqo", "fieldtype": "Column Break"}, {"depends_on": "eval: !doc.__islocal && doc.ngo", "fieldname": "contact_nid_back", "fieldtype": "Attach Image", "label": "National ID Back", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"depends_on": "contact_nid_back", "fieldname": "contact_nid_back_preview", "fieldtype": "Image", "options": "contact_nid_back"}, {"fieldname": "treasurer_details_section", "fieldtype": "Section Break", "label": "Treasurer <PERSON><PERSON>"}, {"fieldname": "treasurer_full_name", "fieldtype": "Data", "label": "Full Name", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_gtwj", "fieldtype": "Column Break"}, {"fieldname": "treasurer_nid", "fieldtype": "Data", "label": "National ID", "read_only": 1, "reqd": 1}, {"collapsible": 1, "fieldname": "section_break_ngjt", "fieldtype": "Section Break"}, {"depends_on": "eval: !doc.__islocal && doc.ngo", "fieldname": "treasurer_nid_front", "fieldtype": "Attach Image", "label": "National ID Front", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"depends_on": "treasurer_nid_front", "fieldname": "image_fibg", "fieldtype": "Image", "options": "treasurer_nid_front"}, {"fieldname": "column_break_jnhl", "fieldtype": "Column Break"}, {"depends_on": "eval: !doc.__islocal && doc.ngo", "fieldname": "treasurer_nid_back", "fieldtype": "Attach Image", "label": "National ID Back", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"depends_on": "treasurer_nid_back", "fieldname": "image_nfxf", "fieldtype": "Image", "options": "treasurer_nid_back"}, {"fieldname": "board_members_details_section", "fieldtype": "Section Break", "label": "Board Members Details"}, {"depends_on": "ngo", "fieldname": "board_members", "fieldtype": "Table", "label": "Board Members", "options": "LR Board Member Details", "reqd": 1}, {"depends_on": "eval: !doc.__islocal && [\"Draft\", \"Pending CN Review\"].includes(doc.status) && doc.ngo", "fieldname": "fetch_ngo_attachments", "fieldtype": "<PERSON><PERSON>", "label": "Fetch NGO Attachments"}, {"fieldname": "beneficiaries", "fieldtype": "Table", "label": "Beneficiaries", "options": "LR Beneficiary", "reqd": 1}, {"fieldname": "section_break_plgp", "fieldtype": "Section Break"}, {"fieldname": "column_break_kkma", "fieldtype": "Column Break"}, {"fieldname": "column_break_qzjg", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "total_loan_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Loan Amount", "non_negative": 1, "options": "currency", "precision": "2", "read_only": 1, "reqd": 1}, {"fieldname": "loan_details_tab", "fieldtype": "Tab Break", "label": "<PERSON>an <PERSON>"}, {"fieldname": "loan_details_section", "fieldtype": "Section Break", "label": "<PERSON>an <PERSON>"}, {"fieldname": "loan_type", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Loan Type", "options": "\nRevolving Loan\nTerm <PERSON>an", "reqd": 1, "translatable": 1}, {"fieldname": "column_break_edpk", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "requested_loan_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Requested <PERSON><PERSON> Amount", "non_negative": 1, "options": "currency", "precision": "2", "reqd": 1}, {"depends_on": "eval: doc.loan_type && doc.requested_loan_amount", "fieldname": "loan_program_details_section", "fieldtype": "Section Break", "label": "Loan Program Details"}, {"fieldname": "loan_program", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Loan Program", "options": "Loan Program", "reqd": 1}, {"fieldname": "column_break_cylf", "fieldtype": "Column Break"}, {"fieldname": "loan_status", "fieldtype": "Data", "label": "Loan Status", "read_only": 1}, {"fieldname": "loan", "fieldtype": "Link", "hidden": 1, "label": "Loan", "options": "Loan"}, {"fieldname": "column_break_wutr", "fieldtype": "Column Break"}, {"default": "Today", "fieldname": "creation_date", "fieldtype": "Date", "label": "Creation Date", "read_only": 1, "reqd": 1}, {"fetch_from": "loan_program.program_name", "fetch_if_empty": 1, "fieldname": "loan_program_name", "fieldtype": "Data", "label": "Name", "read_only": 1, "reqd": 1, "translatable": 1}, {"depends_on": "eval: doc.loan_type && doc.requested_loan_amount && doc.loan_program", "fieldname": "section_break_ziye", "fieldtype": "Section Break"}, {"fetch_from": "loan_program.interest_rate", "fetch_if_empty": 1, "fieldname": "interest_rate", "fieldtype": "Percent", "label": "Interest Rate", "precision": "2", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_owep", "fieldtype": "Column Break"}, {"fetch_from": "loan_program.installment_period", "fetch_if_empty": 1, "fieldname": "installment_period", "fieldtype": "Int", "label": "Installment Period (Years)", "non_negative": 1, "read_only": 1, "reqd": 1}, {"depends_on": "eval: !doc.__islocal", "fieldname": "attachments_tab", "fieldtype": "Tab Break", "label": "Attachments"}, {"fieldname": "lr_document", "fieldtype": "Attach", "label": "Loan Request Document", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"fieldname": "fra_permit", "fieldtype": "Attach", "label": "FRA Permit", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"fieldname": "bod_statement", "fieldtype": "Attach", "label": "Board of Directors Statement", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"fieldname": "terms_tab", "fieldtype": "Tab Break", "label": "Terms & Conditions"}, {"fieldname": "terms", "fieldtype": "Text Editor", "read_only": 1}, {"fieldname": "section_break_oawu", "fieldtype": "Section Break"}, {"fieldname": "column_break_wqtv", "fieldtype": "Column Break"}, {"fieldname": "column_break_aqkm", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "accept_terms", "fieldtype": "Check", "label": "Agree to Terms and Conditions", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"fieldname": "review_tab", "fieldtype": "Tab Break", "label": "Review"}, {"fieldname": "extra_documents_section", "fieldtype": "Section Break", "label": "Extra Documents"}, {"fieldname": "extra_documents", "fieldtype": "Table", "label": "Extra Documents", "options": "Extra Documents", "read_only_depends_on": "eval: doc.status != \"Pending Extra Information\""}, {"fieldname": "section_break_xeem", "fieldtype": "Section Break", "label": "Higher Committee"}, {"fieldname": "hc_report", "fieldtype": "Attach", "label": "Higher Committee Report", "mandatory_depends_on": "eval: doc.status == \"Pending HC Review\"", "read_only_depends_on": "eval: doc.status != \"Pending HC Review\""}, {"fieldname": "field_investigation_section", "fieldtype": "Section Break", "label": "Field Investigation"}, {"fieldname": "financial_investigations", "fieldtype": "Table", "label": "Financial Investigations", "options": "Financial Investigation Details", "read_only": 1}, {"fieldname": "administrative_investigations", "fieldtype": "Table", "label": "Administrative Investigations", "options": "Administrative Investigation Details", "read_only": 1}, {"fieldname": "oc_statement", "fieldtype": "Attach", "label": "Organizational Chart Statement", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"fieldname": "ceo_details_section", "fieldtype": "Section Break", "label": "CEO <PERSON><PERSON>"}, {"fieldname": "ceo_full_name", "fieldtype": "Data", "label": "Full Name", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_svbm", "fieldtype": "Column Break"}, {"fieldname": "ceo_nid", "fieldtype": "Data", "label": "National ID", "read_only": 1, "reqd": 1}, {"fieldname": "section_break_rrpi", "fieldtype": "Section Break"}, {"depends_on": "eval: !doc.__islocal && doc.ngo", "fieldname": "ceo_nid_front", "fieldtype": "Attach Image", "label": "National ID Front", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"depends_on": "ceo_nid_front", "fieldname": "ceo_nid_front_preview", "fieldtype": "Image", "options": "ceo_nid_front"}, {"fieldname": "column_break_rabw", "fieldtype": "Column Break"}, {"depends_on": "eval: !doc.__islocal && doc.ngo", "fieldname": "ceo_nid_back", "fieldtype": "Attach Image", "label": "National ID Back", "read_only_depends_on": "eval: ![\"Draft\", \"Pending CN Review\"].includes(doc.status)"}, {"depends_on": "ceo_nid_back", "fieldname": "ceo_nid_back_preview", "fieldtype": "Image", "options": "ceo_nid_back"}, {"fieldname": "section_break_yjsu", "fieldtype": "Section Break"}, {"fieldname": "treasurer_iscore_status", "fieldtype": "Select", "label": "i-Score Status", "options": "\nDraft\nPending\nSucceed\nFailed", "read_only": 1, "translatable": 1, "width": "10"}, {"depends_on": "eval: doc.status == \"Pending i-Score\" || doc.treasurer_iscore_report", "fieldname": "treasurer_iscore_result", "fieldtype": "Int", "label": "i-Score Result", "mandatory_depends_on": "eval: doc.status == \"Pending i-Score\"", "non_negative": 1, "read_only_depends_on": "eval: doc.status != \"Pending i-Score\" || ![\"Succeed\", \"Failed\"].includes(doc.treasurer_iscore_status)"}, {"fieldname": "column_break_ybul", "fieldtype": "Column Break"}, {"fieldname": "treasurer_iscore_report", "fieldtype": "Attach", "label": "i-Score Report", "mandatory_depends_on": "eval: doc.status == \"Pending i-Score\"", "read_only_depends_on": "eval: doc.status != \"Pending i-Score\" || ![\"Succeed\", \"Failed\"].includes(doc.treasurer_iscore_status)"}, {"fieldname": "section_break_jqcc", "fieldtype": "Section Break"}, {"fieldname": "ceo_iscore_status", "fieldtype": "Select", "label": "i-Score Status", "options": "\nDraft\nPending\nSucceed\nFailed", "read_only": 1, "translatable": 1, "width": "10"}, {"depends_on": "eval: doc.status == \"Pending i-Score\" || doc.ceo_iscore_report", "fieldname": "ceo_iscore_result", "fieldtype": "Int", "label": "i-Score Result", "mandatory_depends_on": "eval: doc.status == \"Pending i-Score\"", "non_negative": 1, "read_only_depends_on": "eval: doc.status != \"Pending i-Score\" || ![\"Succeed\", \"Failed\"].includes(doc.ceo_iscore_status)"}, {"fieldname": "column_break_mvpi", "fieldtype": "Column Break"}, {"fieldname": "ceo_iscore_report", "fieldtype": "Attach", "label": "i-Score Report", "mandatory_depends_on": "eval: doc.status == \"Pending i-Score\"", "read_only_depends_on": "eval: doc.status != \"Pending i-Score\" || ![\"Succeed\", \"Failed\"].includes(doc.ceo_iscore_status)"}, {"fieldname": "treasurer_iscore_doc", "fieldtype": "Data", "hidden": 1, "label": "i-Score Document", "no_copy": 1, "read_only": 1}, {"fieldname": "ceo_iscore_doc", "fieldtype": "Data", "hidden": 1, "label": "i-Score Document", "no_copy": 1, "read_only": 1}, {"fieldname": "treasurer_iscore_report_id", "fieldtype": "Data", "hidden": 1, "label": "Attachment ID", "mandatory_depends_on": "eval: doc.status == \"Pending i-Score\"", "read_only": 1}, {"fieldname": "treasurer_iscore_loans_number", "fieldtype": "Int", "hidden": 1, "label": "i-Score Loans Number", "non_negative": 1}, {"fieldname": "treasurer_iscore_total_balance", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "i-Score Total Balance", "non_negative": 1, "precision": "2"}, {"fieldname": "ceo_iscore_loans_number", "fieldtype": "Int", "hidden": 1, "label": "i-Score Loans Number", "non_negative": 1}, {"fieldname": "ceo_iscore_total_balance", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "label": "i-Score Total Balance", "non_negative": 1, "precision": "2"}, {"fieldname": "ceo_iscore_report_id", "fieldtype": "Data", "hidden": 1, "label": "Attachment ID", "mandatory_depends_on": "eval: doc.status == \"Pending i-Score\"", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-05-20 16:11:11.681818", "modified_by": "Administrator", "module": "Loan", "name": "Loan Request", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "permlevel": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Loan Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Loan Supervisor", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Loan Officer", "share": 1, "write": 1}, {"email": 1, "export": 1, "permlevel": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Loan Officer", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "if_owner": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Customer", "share": 1, "write": 1}], "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "ngo_name", "track_changes": 1, "translated_doctype": 1}