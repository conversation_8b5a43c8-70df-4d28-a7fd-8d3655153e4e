// Copyright (c) 2024, BrainWise and contributors
// For license information, please see license.txt
frappe.ui.form.on("Loan Request", {
    async before_workflow_action(frm) {
        await frm.trigger("request_iscore")
    },
    async request_iscore(frm) {
        if (frm.selected_workflow_action == "Request i-Score") {
            let promise = new Promise((resolve, reject) => {
                frappe.dom.unfreeze()
                let isocreDialog = new frappe.ui.Dialog({
                    title: __("Select i-Score Options"),
                    fields: [
                        {
                            label: __("Inquiry Loan Type"),
                            fieldname: 'inquiry_loan_type',
                            fieldtype: 'Select',
                            options: INQUIRY_LOAN_TYPE,
                            default: "Others (999)",
                            reqd: 1
                        },
                        {
                            label: __("Inquiry Reason"),
                            fieldname: 'inquiry_reason',
                            fieldtype: 'Select',
                            options: INQUIRY_REASON,
                            default: "Renewal Existing Credit Limits (12)",
                            reqd: 1
                        }
                    ],
                    primary_action_label: __("Score"),
                    primary_action(values) {
                        frm.call("request_iscore", { inquiry_loan_type: values.inquiry_loan_type, inquiry_reason: values.inquiry_reason }).then((res) => {
                            let status = res.message
                            isocreDialog.hide();
                            if (status) {
                                frappe.show_alert({
                                    message: __('i-Score Requested Successfully'),
                                    indicator: 'green'
                                }, 5);
                                resolve()
                            } else {
                                reject()
                            }
                        });
                    }
                });
                isocreDialog.show();
            })
            await promise.catch(() => {
                frappe.show_alert({
                    message: __('i-Score Request Failed'),
                    indicator: 'red'
                }, 5);
            });
            return false; // Prevent the default workflow action
        }
    },
    setup(frm) {
        frm.set_query('loan_program', () => {
            return {
                filters: [
                    ["disabled", "=", 0],
                    ["min_amount", "<=", frm.doc.requested_loan_amount],
                    ["max_amount", ">=", frm.doc.requested_loan_amount],
                    ["remaining_funds", ">=", frm.doc.requested_loan_amount]
                ]
            }
        })
    },
    refresh(frm) {
        setCustomerNGO(frm)
        frm.get_field("board_members").grid.only_sortable()
        frm.refresh_field("board_members")
        showFieldInvestigationButtons(frm)
        showLoanButtons(frm)
    },
    ngo(frm) {
        if (frm.doc.ngo) {
            fetchNGOMembers(frm)
        } else {
            frm.call("clear_ngo_members").then(r => {
                reloadSideBar(frm)
            })
        }
    },
    clear_ngo(frm) {
        frm.set_value("ngo", "");
    },
    fetch_ngo_attachments(frm) {
        fetchNGOAttachments(frm)
    },
});

function setCustomerNGO(frm) {
    if (!frm.doc.ngo) {
        frm.call("set_customer_ngo")
    }
}

function fetchNGOMembers(frm) {
    frm.call("fetch_ngo_members")
}

function fetchNGOAttachments(frm) {
    frm.call("fetch_ngo_members_attachments").then(r => {
        reloadSideBar(frm)
        frm.dirty()
    })
}

function reloadSideBar(frm) {
    if (!frm.is_new()) {
        frm.sidebar.reload_docinfo()
    }
}

function showFieldInvestigationButtons(frm) {
    if (frm.doc.status == "Pending Field Investigation") {
        frm.call("has_field_investigation_permissions").then(r => {
            let hasPermission = r.message
            if (hasPermission) {
                showFinancialInvestigationButtons(frm)
                showAdministrativeInvestigationButtons(frm)
            }
        })
    }
}

function showFinancialInvestigationButtons(frm) {
    let current_investigation = false
    frm.doc.financial_investigations.forEach(investigation => {
        if (["Opened", "In Progress"].includes(investigation.status)) {
            current_investigation = true
        }
    });
    if (current_investigation) {
        frm.add_custom_button(__('Delete Financial Investigation'), () => {
            frm.call({
                doc: frm.doc,
                method: "delete_investigation",
                freeze: true,
                args: { investigation: "Financial Investigation" }
            }).then((r) => {
                frm.reload_doc()
            });
        })
    } else {
        frm.add_custom_button(__('New Financial Investigation'), () => {
            frappe.prompt({
                label: __("Investigator"),
                fieldname: 'investigator',
                fieldtype: 'Link',
                options: 'User',
                reqd: true,
                get_query: function () {
                    return {
                        query: "parent_ngo.utils.user.user_query",
                        filters: [["Has Role", "role", "=", "NGO Investigator"]]
                    }
                }
            }, (values) => {
                frm.call({
                    doc: frm.doc,
                    method: "create_investigation",
                    freeze: true,
                    args: { investigation: "Financial Investigation", investigator: values.investigator }
                }).then((r) => {
                    frm.reload_doc()
                });
            })
        })
    }
}

function showAdministrativeInvestigationButtons(frm) {
    let current_investigation = false
    frm.doc.administrative_investigations.forEach(investigation => {
        if (["Opened", "In Progress"].includes(investigation.status)) {
            current_investigation = true
        }
    });
    if (current_investigation) {
        frm.add_custom_button(__('Delete Administrative Investigation'), () => {
            frm.call({
                doc: frm.doc,
                method: "delete_investigation",
                freeze: true,
                args: { investigation: "Administrative Investigation" }
            }).then((r) => {
                frm.reload_doc()
            });
        })
    } else {
        frm.add_custom_button(__('New Administrative Investigation'), () => {
            frappe.prompt({
                label: __("Investigator"),
                fieldname: 'investigator',
                fieldtype: 'Link',
                options: 'User',
                reqd: true,
                get_query: function () {
                    return {
                        query: "parent_ngo.utils.user.user_query",
                        filters: [["Has Role", "role", "=", "NGO Investigator"]]
                    }
                }
            }, (values) => {
                frm.call({
                    doc: frm.doc,
                    method: "create_investigation",
                    freeze: true,
                    args: { investigation: "Administrative Investigation", investigator: values.investigator }
                }).then((r) => {
                    frm.reload_doc()
                });
            })
        })
    }
}

function showLoanButtons(frm) {
    if (frm.doc.status == "Approved") {
        frappe.call("parent_ngo.utils.loan.get_loan_permissions").then(r => {
            let permissions = r.message
            if (["Sanctioned", "Partially Disbursed"].includes(frm.doc.status)) {
                if (permissions.loan_disbursement) {
                    frm.add_custom_button(__('Loan Disbursement'), function () {
                        frm.trigger("make_loan_disbursement");
                    }, __('Create'));
                }
            }
            if (["Partially Disbursed", "Disbursed"].includes(frm.doc.status)) {
                if (permissions.loan_repayment) {
                    frm.add_custom_button(__('Loan Repayment'), function () {
                        frm.trigger("make_repayment_entry");
                    }, __('Create'));
                }
            }
            if (frm.doc.loan) {
                if (permissions.read_loan) {
                    frm.add_custom_button(__('Go To Loan'), () => {
                        frappe.open_in_new_tab = true
                        frappe.set_route("Form", "Loan", frm.doc.loan);
                    })
                }
            } else {
                if (permissions.create_loan) {
                    frm.add_custom_button(__('Create Loan'), () => {
                        frm.call({
                            doc: frm.doc,
                            args: { save: true },
                            method: "create_loan",
                            freeze: true
                        }).then((r) => {
                            let loanDocID = r.message
                            frappe.open_in_new_tab = true
                            frappe.set_route("Form", "Loan", loanDocID);
                        });
                    })
                }
            }
        })
    }
}

frappe.ui.form.on('LR Beneficiary', {
    nid: function (frm, cdt, cdn) {
        let beneficiaryRow = locals[cdt][cdn]
        let nid = beneficiaryRow.nid
        frm.call("get_beneficiary_by_nid", { nid: nid }).then(r => {
            let beneficiary = r.message
            if (beneficiary) {
                beneficiaryRow.beneficiary_doc = beneficiary.name
                beneficiaryRow.nid = beneficiary.nid
                beneficiaryRow.full_name = beneficiary.full_name
            } else if (beneficiaryRow.beneficiary_doc) {
                beneficiaryRow.beneficiary_doc = null
                beneficiaryRow.nid = null
                beneficiaryRow.full_name = null
            }
            frm.refresh_field("beneficiaries")
        })
    },
    loan_amount: function (frm, cdt, cdn) {
        calculateBeneficiariesTotalLoanAmount(frm)
    },
    beneficiaries_remove: function (frm, cdt, cdn) {
        calculateBeneficiariesTotalLoanAmount(frm)
    }
});

function calculateBeneficiariesTotalLoanAmount(frm) {
    let totalLoanAmount = 0
    frm.doc.beneficiaries.forEach(beneficiary => {
        totalLoanAmount += beneficiary.loan_amount
    })
    frm.set_value("total_loan_amount", totalLoanAmount)
    frm.set_value("requested_loan_amount", totalLoanAmount)
}

// Constants for i-Score inquiry options
let INQUIRY_LOAN_TYPE = [
    "Agriculture Activities (018)",
    "Agriculture Investments (019)",
    "Letter of Credit (020)",
    "Letter of Guarantee (021)",
    "Revolving Credit (022)",
    "Micro Finance (023)",
    "Leasing Finance (024)",
    "Islamic Facilities (025)",
    "Gas Loan (026)",
    "Group Lending (027)",
    "Taxi Loan (028)",
    "Taxi Insurance (029)",
    "Club Loan (030)",
    "Expenditure Payment (031)",
    "Credit Sales (032)",
    "Machinery Loan (033)",
    "Industrial Activities (034)",
    "Trade Activities (035)",
    "Service Activities (036)",
    "MASTORA Facility (037)",
    "Commercial Discount Papers (038)",
    "Mortgage Loan (050)",
    "Social Housing Loan (051)",
    "Gas Invoices (052)",
    "Others (999)"
]

let INQUIRY_REASON = [
    "Granting New Facility (11)",
    "Renewal Existing Credit Limits (12)",
    "Modifying Existing Credit Limits (13)",
    "Review Customer Credit Position (14)",
    "Based on Court Ruling (15)",
    "Acceptance of Guarantee (16)",
    "Based on Authorization (17)",
    "Study Complaint (18)"
]
