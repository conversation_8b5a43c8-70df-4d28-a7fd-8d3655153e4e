# Change Log
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/)
and this project adheres to [Semantic Versioning](http://semver.org/).

# [Unreleased] - YYYY-MM-DD

## Added

- Add the following Attachments to the `Customer NGO` DocType and the `Customer NGO Onboarding` Web Form
	- Board Approval Minutes for Funding
	- Financial and Administrative Report
	- Premises Proof Document
	- Credit Inquiry Declaration
	- Bank Account Declaration
- Add "Committee Decision" select field in Committee Decision section for both administrative and financial investigations
- Add new field named `Association Headquarters Address` in `Administrative Investigation` doctype in `NGO Address` tab
- Add `CEO Info` section to `Customer NGO` doctype (same fields as `Treasurer` section).
- Add `CEO Info` page to onboarding form of new `Customer NGO`
- Add new attach field named `Signed and Stamped Repayment Schedule` in `Loan Disbursement`
- Add new report `Loan Installments`
- Add a new field `Donations Record` to the `Financial Investigation` Doctype
- Add new fields to Financial Records for `Financial Investigation` Doctype
	- Bank Book
	- Assets Register
- Add new Child Table `Cash Flow`
- Add new section `Cash Flow Form` to the `Financial Investigation` Doctype
- Apply the Scoring logic for the `Financial Investigation`
- Add a new section `CEO Details` to the `Loan Request` doctype
- Add I-Score section to the `Loan Request` doctype for both `Treasurer` and `CEO`

## Changed

- Disable the Frappe feature of Saving the Filters
    - Ensure that no filters are applied when loading the list view
- Add `Signed and Stamped Repayment Schedule` in `validate_attachments` method
- Translate `Signed and Stamped Repayment Schedule`
- Rename `Financial Cycle` tab in `Financial Investigation` to `Financial Document Cycle`
- Translate `Financial Document Cycle` instead of `Financial Cycle`
- Add conditional validation for the `Association Headquarters Address` field in `validate_mandatory_fields` method, making it mandatory
- Add `association_headquarters_address` field in iam queries.py
- Change `Details` tab name to `Association (Institution) Data`
- Translate `Association (Institution) Data`

## Fixed

- Change the fields' label that are common in the NGO

## Removed

- Delete the option of "5 Records" in `Export Data` tool
- Diabale `>`, `<`, `>=`, `<=` for any text field in the filters

# [v0.6.0] - 2024-12-27

## Added

- Translate the DocType's history and Audit Log

## Fixed

- Hide `Cost Center`, `Project`, `Finance Book` filter fields from the Trial Balance report
- Remove Edit from actions menu (disable bulk edit)

# [v0.5.0] - 2024-12-12

## Added

- Add some translation

## Fixed

- Validate the lending End Date for Microfinance Activities
- Fix the NIDs Validation - Must be digits

# [v0.4.0] - 2024-11-25

## Added

- Add some new translations

## Fixed

- Fixed Sidebar Filters stuck at "Loading ..." on Int Fields
- Fixed Report View group by table fields

## [v0.3.0] - 2024-11-07

### Changed

- Change precision of all currency and percent fields to 2
- Hide standard fields from journal entry doctype
- Hide advanced search and create new document for gender link fields

### Fixed

- Fix the GUI of the Sort by field in list view Drop-Down in Arabic Language
- Fixed Test Issues #3:
	- General_7, General_23, General_54, general_61, general_80, general_81

## [v0.2.0] - 2024-11-04

### Added

- Integrate IAM Response Handling, Methods Whitelisting and Exception Throwing
- Validate License Manager Client is installed before migration & before requests
- Set default IAM Settings on migration
- Applied translations through CSV file
- Clear role profiles before migration
- Set default system settings on migration
- Clear list view settings before migration
- Limit user doctype fields depending on roles
- Apply localization on Workflow Help Dialog
- Apply localization on Bulk Clear Assignment Dialog
- Edit User doctype fields
	- Show extra fields in User Quick Entry Dialog
		- Last Name
		- Language
		- Password
	- Set language default to arabic
- APIs
	- Implement v1 RESTFul APIs for all existing API Endpoints

### Changed

- Applied default doctypes permissions on doctype settings instead of Custom DocPerms
- Set role profile on customer ngo user account creation
- Show all tags in tags filter ignoring current filters

### Fixed

- Export Serial No Warranty Expiry report from ERPNext to fix migration error
- Make tags filter consider localization
- Fixed Test Issues #1:
	- General_2, General_5, General_6, General_10, General_11, General_13, General_14, General_15, General_17, General_20, General_22, General_25, General_35, General_36, General_38, General_39, General_40, General_42, parent_2, parent_6, parent_12
- Fixed Test Issues #2:
	- General_3, General_4, General_8, General_12, General_18, General_19, General_20, General_22, General_27, General_28, General_43, General_44, General_45, General_46, General_47, General_48, General_49, General_52, General_53, general_56, general_58, General_59, General_60, general_61, general_63, General_64, General_65, general_66, general_67, General_69, general_70, General_71, general_72, general_73, General_74, General_75, general_76, general_77, General_78, General_79, General_82, general_83, general_85, general_86, general_87, general_88, general_89, general_90, general_92, general_93, general_94, general_95, general_96, general_97, general_98, general_99, Parent_2, Parent_6, Parent_12, parent_16, parent_17, parent_18, parent_19

### Removed

- Hide document follow button
- Hide Events, What's New and Settings buttons from notifications dropdown
- Hide view dropdown button in chart of accounts tree view
- Hide link field filter description
- Hide workflow user actions from list view


## [v0.1.0] - 2024-10-12

### Added

- Created Beneficiary DocType and its dependent DocTypes
- Created Customer NGO DocType and its dependent DocTypes
- Created Loan Module
- Created Loan Program DocType and its dependent DocTypes
- Created Loan Request DocType and its dependent DocTypes
- Implemented Loan Request Workflow
- Created Default Roles:
    - NGO Loan Officer
    - NGO Loan Supervisor
    - NGO Loan Manager
    - NGO Investigator
    - NGO Customer
- Created Financial Investigation DocType and its dependent DocTypes
- Implemented Financial Investigation APIs:
	- Add Get Assigned Financial Investigations
	- Add Get Financial Investigation details
	- Update Financial Investigation
	- Set completion Financial Investigation
- Created Administrative Investigation DocType and its dependent DocTypes
- Implemented Administrative Investigation APIs:
	- Add Get Assigned Administrative Investigations Endpoint
	- Add Get Administrative Investigation details
	- Update Administrative Investigation
	- Set completion Administrative Investigation
- Implemented Field Investigation Workflow
- Implemented Customer NGO Workflow
- Automatically create user account for approved Customer NGO
- Created Loan DocType and its dependent DocTypes
- Created Loan Disbursement and its dependent DocTypes
- Created Loan Disbursement Request and its dependent DocTypes
- Created Loan Repayment and its dependent DocTypes
- Created Financial Follow Up and its dependent DocTypes
- Implemented Financial Follow Up APIs:
	- Get Assigned Financial Follow Ups
	- Get Financial Follow Up Details
	- Update Financial Follow Up
	- Set Financial Follow Up Completion Status
- Created Administrative Follow Up and its dependent DocTypes
- Implemented Administrative Follow Up APIs:
	- Get Assigned Administrative Follow Ups
	- Get Administrative Follow Up Details
	- Update Administrative Follow Up
	- Set Administrative Follow Up Completion Status
- Apply Rate Limiter on all API Endpoints
- Split Financial & Loan Records Tabs Fields options into separate fields in Financial Investigation and Administrative Follow Up

### Changed

- Correctly arrange next and previous documents buttons in Arabic Language
