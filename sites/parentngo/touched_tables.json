["tabParty Type", "tabWorkflow", "tabFinancial Investigation", "tabDocType Link", "tabDocPerm", "tabHas Role", "tabWorkflow State", "tabPortal", "tabCustom", "tabList View Settings", "tabWorkflow Transition", "tabWorkspace Link", "tabList", "tabLoan", "tabFinancial", "tabInstalled", "tabWorkspace Quick List", "tabAttachment", "tabIAM", "tabWorkspace Shortcut", "tabProperty", "tabFinancial Follow Up", "tabWebsite Route Redirect", "tabTag Link", "tabProperty Setter", "tabPortal Menu Item", "tabVersion", "tabCustomer NGO", "tabDocField", "tabWorkspace Chart", "tabAdministrative Follow Up", "tabRole Workspace", "tabTag", "tabLoan Disbursement Request", "tabParty", "tabLoan Request", "tabPrint Heading", "tabCustom Role", "tabRole Profile", "tabDocType", "tabIAM Role", "tabWorkspace", "tabCustom DocPerm", "tabPrint", "tabWebsite", "tabPolicy", "tabTop", "tabAPI Response", "tabWorkspace Custom Block", "tabAPI Response Target", "tabAdministrative Investigation", "tabHas", "tabInstalled Application", "tabNavbar <PERSON>em", "tabAPI", "tabSingles", "tabEmail Template", "tabRole", "tabWorkspace Number Card", "tabWorkflow Action Master", "tabSeries", "tabCustom Field", "tabAdministrative", "tabTop Bar Item", "tabDocType Action", "tabDocType State", "tabWorkflow Document State", "tabNavbar", "tabCustomer", "tabEmail"]